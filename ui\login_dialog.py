# -*- coding: utf-8 -*-
"""
Login dialog for the Accounting System
Handles user authentication and role-based access
"""

import sys
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QMessageBox, QFrame,
                            QApplication, QWidget, QGridLayout, QSpacerItem,
                            QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPalette, QIcon
from db.connection import authenticate_user
from config import APP_CONFIG, UI_CONFIG, MESSAGES
import logging

logger = logging.getLogger(__name__)

class LoginDialog(QDialog):
    """Login dialog with modern design"""
    
    # Signal emitted when login is successful
    login_successful = pyqtSignal(dict)  # Emits user data
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.user_data = None
        self.setup_ui()
        self.setup_connections()
        self.apply_styles()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("تسجيل الدخول - " + APP_CONFIG['app_name'])
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 30, 40, 30)
        
        # Title
        title_label = QLabel(APP_CONFIG['app_name'])
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont(UI_CONFIG['font_family'], UI_CONFIG['title_font_size'], QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {UI_CONFIG['primary_color']}; margin-bottom: 20px;")
        
        # Login form frame
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_layout = QGridLayout(form_frame)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(20, 20, 20, 20)
        
        # Username field
        username_label = QLabel("اسم المستخدم:")
        username_label.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['font_size']))
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        self.username_edit.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['font_size']))
        
        # Password field
        password_label = QLabel("كلمة المرور:")
        password_label.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['font_size']))
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['font_size']))
        
        # Add fields to form
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(self.username_edit, 0, 1)
        form_layout.addWidget(password_label, 1, 0)
        form_layout.addWidget(self.password_edit, 1, 1)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['font_size'], QFont.Bold))
        self.login_button.setDefault(True)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['font_size']))
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.login_button)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['font_size'] - 1))
        self.status_label.hide()
        
        # Add to main layout
        main_layout.addWidget(title_label)
        main_layout.addWidget(form_frame)
        main_layout.addLayout(button_layout)
        main_layout.addWidget(self.status_label)
        main_layout.addStretch()
        
        self.setLayout(main_layout)
        
        # Set focus to username field
        self.username_edit.setFocus()
    
    def setup_connections(self):
        """Setup signal connections"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.password_edit.returnPressed.connect(self.handle_login)
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)
    
    def apply_styles(self):
        """Apply modern styles to the dialog"""
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {UI_CONFIG['background_color']};
            }}
            
            QFrame {{
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
            }}
            
            QLineEdit {{
                padding: 8px 12px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: {UI_CONFIG['font_size']}px;
                min-height: 20px;
            }}
            
            QLineEdit:focus {{
                border-color: {UI_CONFIG['primary_color']};
            }}
            
            QPushButton {{
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
                min-height: 35px;
            }}
            
            QPushButton#login_button {{
                background-color: {UI_CONFIG['primary_color']};
                color: white;
            }}
            
            QPushButton#login_button:hover {{
                background-color: #1e5f7a;
            }}
            
            QPushButton#login_button:pressed {{
                background-color: #164a5e;
            }}
            
            QPushButton#cancel_button {{
                background-color: #6c757d;
                color: white;
            }}
            
            QPushButton#cancel_button:hover {{
                background-color: #545b62;
            }}
            
            QLabel {{
                color: {UI_CONFIG['text_color']};
            }}
        """)
        
        # Set object names for styling
        self.login_button.setObjectName("login_button")
        self.cancel_button.setObjectName("cancel_button")
    
    def handle_login(self):
        """Handle login button click"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        # Validate input
        if not username:
            self.show_error("يرجى إدخال اسم المستخدم")
            self.username_edit.setFocus()
            return
        
        if not password:
            self.show_error("يرجى إدخال كلمة المرور")
            self.password_edit.setFocus()
            return
        
        # Show loading status
        self.show_status("جاري التحقق من البيانات...", "info")
        self.login_button.setEnabled(False)
        
        # Use QTimer to allow UI to update before authentication
        QTimer.singleShot(100, lambda: self.authenticate_user(username, password))
    
    def authenticate_user(self, username: str, password: str):
        """Authenticate user credentials"""
        try:
            user_data = authenticate_user(username, password)
            
            if user_data:
                self.user_data = user_data
                self.show_status("تم تسجيل الدخول بنجاح", "success")
                
                # Emit success signal and close dialog
                QTimer.singleShot(500, self.accept_login)
            else:
                self.show_error(MESSAGES['error']['login'])
                self.password_edit.clear()
                self.password_edit.setFocus()
                
        except Exception as e:
            logger.error(f"Login error: {e}")
            self.show_error(MESSAGES['error']['connection'])
        
        finally:
            self.login_button.setEnabled(True)
    
    def accept_login(self):
        """Accept login and emit signal"""
        self.login_successful.emit(self.user_data)
        self.accept()
    
    def show_error(self, message: str):
        """Show error message"""
        self.show_status(message, "error")
    
    def show_status(self, message: str, status_type: str = "info"):
        """Show status message with appropriate color"""
        self.status_label.setText(message)
        self.status_label.show()
        
        if status_type == "error":
            color = UI_CONFIG['danger_color']
        elif status_type == "success":
            color = UI_CONFIG['success_color']
        elif status_type == "warning":
            color = UI_CONFIG['warning_color']
        else:
            color = UI_CONFIG['text_color']
        
        self.status_label.setStyleSheet(f"color: {color}; font-weight: bold;")
        
        # Auto-hide status after 3 seconds for non-error messages
        if status_type != "error":
            QTimer.singleShot(3000, self.status_label.hide)
    
    def get_user_data(self) -> dict:
        """Get authenticated user data"""
        return self.user_data
    
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)

# Test the login dialog
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName(APP_CONFIG['app_name'])
    app.setApplicationVersion(APP_CONFIG['version'])
    
    # Create and show login dialog
    login_dialog = LoginDialog()
    
    def on_login_success(user_data):
        print(f"Login successful: {user_data}")
        app.quit()
    
    login_dialog.login_successful.connect(on_login_success)
    
    if login_dialog.exec_() == QDialog.Accepted:
        print("Login dialog accepted")
    else:
        print("Login dialog cancelled")
    
    sys.exit()
