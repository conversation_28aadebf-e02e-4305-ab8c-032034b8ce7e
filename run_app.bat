@echo off
echo Starting Accounting System...
echo نظام المحاسبة - جاري التشغيل...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH
    echo يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

REM Install requirements if needed
if not exist "venv" (
    echo Creating virtual environment...
    echo إنشاء البيئة الافتراضية...
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Install requirements
echo Installing requirements...
echo تثبيت المتطلبات...
pip install -r requirements.txt

REM Run the application
echo Starting application...
echo تشغيل التطبيق...
python main.py

pause
