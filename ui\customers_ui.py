# -*- coding: utf-8 -*-
"""
Customers management UI module
Handles CRUD operations for customers with balance tracking
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
                            QHeaderView, QGroupBox, QTextEdit, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from db.repositories import CustomersRepository
from config import UI_CONFIG, MESSAGES, VALIDATION_RULES
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)

class CustomerDialog(QDialog):
    """Dialog for adding/editing customers"""
    
    def __init__(self, customer_data=None, parent=None):
        super().__init__(parent)
        self.customer_data = customer_data
        self.is_edit_mode = customer_data is not None
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_customer_data()
    
    def setup_ui(self):
        """Setup dialog UI"""
        title = "تعديل عميل" if self.is_edit_mode else "إضافة عميل جديد"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(450, 350)
        
        layout = QVBoxLayout(self)
        
        # Form
        form_group = QGroupBox("بيانات العميل")
        form_layout = QFormLayout(form_group)
        
        # Name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم العميل")
        form_layout.addRow("اسم العميل:", self.name_edit)
        
        # Phone
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف")
        form_layout.addRow("رقم الهاتف:", self.phone_edit)
        
        # Address
        self.address_edit = QTextEdit()
        self.address_edit.setPlaceholderText("أدخل العنوان")
        self.address_edit.setMaximumHeight(80)
        form_layout.addRow("العنوان:", self.address_edit)
        
        # Active status (for edit mode)
        if self.is_edit_mode:
            self.active_checkbox = QCheckBox("العميل نشط")
            form_layout.addRow("الحالة:", self.active_checkbox)
            
            # Show current balance (read-only)
            self.balance_label = QLabel("0.00")
            self.balance_label.setStyleSheet("font-weight: bold; color: #007bff;")
            form_layout.addRow("الرصيد الحالي:", self.balance_label)
        
        layout.addWidget(form_group)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.button(QDialogButtonBox.Ok).setText("حفظ")
        button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
        
        self.button_box = button_box
    
    def setup_connections(self):
        """Setup signal connections"""
        self.button_box.accepted.connect(self.validate_and_accept)
        self.button_box.rejected.connect(self.reject)
    
    def load_customer_data(self):
        """Load customer data for editing"""
        if self.customer_data:
            self.name_edit.setText(self.customer_data.get('name', ''))
            self.phone_edit.setText(self.customer_data.get('phone', ''))
            self.address_edit.setPlainText(self.customer_data.get('address', ''))
            
            if hasattr(self, 'active_checkbox'):
                self.active_checkbox.setChecked(self.customer_data.get('isactive', True))
            
            if hasattr(self, 'balance_label'):
                balance = self.customer_data.get('balance', 0)
                self.balance_label.setText(f"{balance:.2f}")
    
    def validate_and_accept(self):
        """Validate form data and accept dialog"""
        # Get form data
        name = self.name_edit.text().strip()
        phone = self.phone_edit.text().strip()
        address = self.address_edit.toPlainText().strip()
        
        # Validate name
        if not name:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم العميل")
            return
        
        from config import validate_field
        is_valid, error_msg = validate_field('name', name)
        if not is_valid:
            QMessageBox.warning(self, "خطأ", f"اسم العميل غير صحيح: {error_msg}")
            return
        
        # Validate phone
        if not phone:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الهاتف")
            return
        
        is_valid, error_msg = validate_field('phone', phone)
        if not is_valid:
            QMessageBox.warning(self, "خطأ", f"رقم الهاتف غير صحيح: {error_msg}")
            return
        
        self.accept()
    
    def get_form_data(self):
        """Get form data"""
        data = {
            'Name': self.name_edit.text().strip(),
            'Phone': self.phone_edit.text().strip(),
            'Address': self.address_edit.toPlainText().strip()
        }
        
        # Add active status for edit mode
        if self.is_edit_mode and hasattr(self, 'active_checkbox'):
            data['IsActive'] = self.active_checkbox.isChecked()
        
        return data

class CustomersWidget(QWidget):
    """Main customers management widget"""
    
    def __init__(self, current_user_data, parent=None):
        super().__init__(parent)
        self.current_user_data = current_user_data
        self.customers_repo = CustomersRepository()
        self.setup_ui()
        self.setup_connections()
        self.load_customers()
    
    def setup_ui(self):
        """Setup widget UI"""
        layout = QVBoxLayout(self)
        
        # Toolbar
        toolbar_layout = QHBoxLayout()
        
        self.add_button = QPushButton("إضافة عميل")
        self.edit_button = QPushButton("تعديل")
        self.delete_button = QPushButton("حذف")
        self.refresh_button = QPushButton("تحديث")
        self.balance_button = QPushButton("عرض الأرصدة")
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في العملاء...")
        self.search_button = QPushButton("بحث")
        
        toolbar_layout.addWidget(self.add_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addWidget(self.delete_button)
        toolbar_layout.addWidget(self.balance_button)
        toolbar_layout.addWidget(self.refresh_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(QLabel("البحث:"))
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(self.search_button)
        
        # Table
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "رقم العميل", "اسم العميل", "رقم الهاتف", "العنوان", "الرصيد", "الحالة"
        ])
        
        # Configure table
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        
        layout.addLayout(toolbar_layout)
        layout.addWidget(self.table)
        
        # Apply styles
        self.apply_styles()
    
    def setup_connections(self):
        """Setup signal connections"""
        self.add_button.clicked.connect(self.add_customer)
        self.edit_button.clicked.connect(self.edit_customer)
        self.delete_button.clicked.connect(self.delete_customer)
        self.refresh_button.clicked.connect(self.load_customers)
        self.balance_button.clicked.connect(self.show_balances)
        self.search_button.clicked.connect(self.search_customers)
        self.search_edit.returnPressed.connect(self.search_customers)
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        self.table.itemDoubleClicked.connect(self.edit_customer)
    
    def apply_styles(self):
        """Apply styles to the widget"""
        self.setStyleSheet(f"""
            QPushButton {{
                padding: 8px 16px;
                margin: 2px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }}
            
            QPushButton:hover {{
                opacity: 0.8;
            }}
            
            QPushButton#add {{
                background-color: {UI_CONFIG['success_color']};
                color: white;
            }}
            
            QPushButton#edit {{
                background-color: {UI_CONFIG['primary_color']};
                color: white;
            }}
            
            QPushButton#delete {{
                background-color: {UI_CONFIG['danger_color']};
                color: white;
            }}
            
            QPushButton#balance {{
                background-color: {UI_CONFIG['secondary_color']};
                color: white;
            }}
            
            QPushButton#refresh {{
                background-color: #6c757d;
                color: white;
            }}
            
            QLineEdit {{
                padding: 6px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }}
            
            QTableWidget {{
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }}
            
            QTableWidget::item {{
                padding: 8px;
            }}
            
            QHeaderView::section {{
                background-color: {UI_CONFIG['primary_color']};
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }}
        """)
        
        # Set object names for styling
        self.add_button.setObjectName("add")
        self.edit_button.setObjectName("edit")
        self.delete_button.setObjectName("delete")
        self.balance_button.setObjectName("balance")
        self.refresh_button.setObjectName("refresh")
    
    def load_customers(self):
        """Load customers from database"""
        try:
            customers = self.customers_repo.get_customers_with_balances()
            self.populate_table(customers)
        except Exception as e:
            logger.error(f"Error loading customers: {e}")
            QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def populate_table(self, customers):
        """Populate table with customer data"""
        self.table.setRowCount(len(customers))
        
        for row, customer in enumerate(customers):
            self.table.setItem(row, 0, QTableWidgetItem(str(customer['customerid'])))
            self.table.setItem(row, 1, QTableWidgetItem(customer['name']))
            self.table.setItem(row, 2, QTableWidgetItem(customer['phone']))
            self.table.setItem(row, 3, QTableWidgetItem(customer.get('address', '')))
            
            # Balance with formatting
            balance = float(customer.get('balance', 0))
            balance_item = QTableWidgetItem(f"{balance:.2f}")
            if balance > 0:
                balance_item.setBackground(Qt.lightGreen)
            elif balance < 0:
                balance_item.setBackground(Qt.lightCoral)
            self.table.setItem(row, 4, balance_item)
            
            # Status
            status = "نشط" if customer.get('isactive', True) else "غير نشط"
            status_item = QTableWidgetItem(status)
            if not customer.get('isactive', True):
                status_item.setBackground(Qt.lightGray)
            self.table.setItem(row, 5, status_item)
            
            # Store customer data in first column
            self.table.item(row, 0).setData(Qt.UserRole, customer)
    
    def on_selection_changed(self):
        """Handle table selection change"""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
    
    def get_selected_customer(self):
        """Get selected customer data"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            return self.table.item(current_row, 0).data(Qt.UserRole)
        return None
    
    def add_customer(self):
        """Add new customer"""
        dialog = CustomerDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                form_data = dialog.get_form_data()
                self.customers_repo.insert(form_data)
                QMessageBox.information(self, "نجح", MESSAGES['success']['save'])
                self.load_customers()
            except Exception as e:
                logger.error(f"Error adding customer: {e}")
                if "duplicate" in str(e).lower() or "unique" in str(e).lower():
                    QMessageBox.warning(self, "خطأ", "رقم الهاتف مستخدم مسبقاً")
                else:
                    QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def edit_customer(self):
        """Edit selected customer"""
        customer_data = self.get_selected_customer()
        if not customer_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
            return
        
        dialog = CustomerDialog(customer_data, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                form_data = dialog.get_form_data()
                self.customers_repo.update('CustomerID', customer_data['customerid'], form_data)
                QMessageBox.information(self, "نجح", MESSAGES['success']['update'])
                self.load_customers()
            except Exception as e:
                logger.error(f"Error updating customer: {e}")
                if "duplicate" in str(e).lower() or "unique" in str(e).lower():
                    QMessageBox.warning(self, "خطأ", "رقم الهاتف مستخدم مسبقاً")
                else:
                    QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def delete_customer(self):
        """Delete selected customer"""
        customer_data = self.get_selected_customer()
        if not customer_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد", 
                                   f"هل تريد حذف العميل '{customer_data['name']}'؟\n"
                                   "تحذير: سيتم حذف جميع البيانات المرتبطة بهذا العميل",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                self.customers_repo.soft_delete('CustomerID', customer_data['customerid'])
                QMessageBox.information(self, "نجح", "تم حذف العميل")
                self.load_customers()
            except Exception as e:
                logger.error(f"Error deleting customer: {e}")
                if "constraint" in str(e).lower():
                    QMessageBox.warning(self, "خطأ", MESSAGES['error']['delete_constraint'])
                else:
                    QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def show_balances(self):
        """Show customers with balances only"""
        try:
            customers = self.customers_repo.get_customers_with_balances()
            # Filter customers with non-zero balances
            customers_with_balance = [c for c in customers if float(c.get('balance', 0)) != 0]
            self.populate_table(customers_with_balance)
        except Exception as e:
            logger.error(f"Error loading customer balances: {e}")
            QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def search_customers(self):
        """Search customers"""
        search_term = self.search_edit.text().strip()
        if not search_term:
            self.load_customers()
            return
        
        try:
            customers = self.customers_repo.search_customers(search_term)
            # Get balances for search results
            customers_with_balances = []
            for customer in customers:
                customer_with_balance = self.customers_repo.get_customer_with_balance(customer['customerid'])
                if customer_with_balance:
                    customers_with_balances.append(customer_with_balance)
            
            self.populate_table(customers_with_balances)
        except Exception as e:
            logger.error(f"Error searching customers: {e}")
            QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
