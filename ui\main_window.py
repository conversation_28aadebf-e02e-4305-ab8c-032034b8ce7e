# -*- coding: utf-8 -*-
"""
Main application window for the Accounting System
Contains navigation sidebar and content area for different modules
"""

import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, 
                            QStackedWidget, QListWidget, QListWidgetItem,
                            QLabel, QPushButton, QFrame, QMessageBox,
                            QMenuBar, QStatusBar, QAction, QToolBar,
                            QSplitter, QApplication)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QPalette
from config import APP_CONFIG, UI_CONFIG, USER_ROLES, MESSAGES
from db.connection import check_permission
import logging

logger = logging.getLogger(__name__)

class NavigationItem:
    """Navigation item data class"""
    def __init__(self, name: str, title: str, permission: str, icon: str = None):
        self.name = name
        self.title = title
        self.permission = permission
        self.icon = icon

class MainWindow(QMainWindow):
    """Main application window"""
    
    # Signals
    module_changed = pyqtSignal(str)  # Emitted when module changes
    user_logout = pyqtSignal()       # Emitted when user logs out
    
    def __init__(self, user_data: dict, parent=None):
        super().__init__(parent)
        self.user_data = user_data
        self.current_module = None
        self.module_widgets = {}
        
        # Define navigation items
        self.navigation_items = [
            NavigationItem("dashboard", "لوحة التحكم", "dashboard", "🏠"),
            NavigationItem("users", "إدارة المستخدمين", "users", "👥"),
            NavigationItem("customers", "إدارة العملاء", "customers", "👤"),
            NavigationItem("suppliers", "إدارة الموردين", "suppliers", "🏢"),
            NavigationItem("products", "إدارة المنتجات", "products", "📦"),
            NavigationItem("sales", "فواتير المبيعات", "sales", "💰"),
            NavigationItem("purchases", "فواتير المشتريات", "purchases", "🛒"),
            NavigationItem("accounts", "الحسابات والقيود", "accounts", "📊"),
            NavigationItem("reports", "التقارير", "reports", "📈"),
            NavigationItem("audit", "سجل المراجعة", "audit", "🔍"),
        ]
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_status_bar()
        self.apply_styles()
        self.load_user_permissions()
        
        # Show dashboard by default
        self.show_module("dashboard")
    
    def setup_ui(self):
        """Setup the main user interface"""
        self.setWindowTitle(f"{APP_CONFIG['window_title']} - {self.user_data['username']}")
        self.setMinimumSize(*APP_CONFIG['min_window_size'])
        self.resize(*APP_CONFIG['window_size'])
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create splitter for resizable sidebar
        splitter = QSplitter(Qt.Horizontal)
        
        # Sidebar
        self.sidebar = self.create_sidebar()
        splitter.addWidget(self.sidebar)
        
        # Content area
        self.content_area = self.create_content_area()
        splitter.addWidget(self.content_area)
        
        # Set splitter proportions
        splitter.setSizes([250, 1150])
        splitter.setCollapsible(0, False)  # Sidebar not collapsible
        splitter.setCollapsible(1, False)  # Content area not collapsible
        
        main_layout.addWidget(splitter)
    
    def create_sidebar(self) -> QWidget:
        """Create navigation sidebar"""
        sidebar = QFrame()
        sidebar.setFixedWidth(250)
        sidebar.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # User info header
        user_header = self.create_user_header()
        layout.addWidget(user_header)
        
        # Navigation list
        self.nav_list = QListWidget()
        self.nav_list.setFrameStyle(QFrame.NoFrame)
        self.nav_list.itemClicked.connect(self.on_navigation_clicked)
        
        layout.addWidget(self.nav_list)
        
        # Logout button
        logout_btn = QPushButton("تسجيل الخروج")
        logout_btn.clicked.connect(self.handle_logout)
        layout.addWidget(logout_btn)
        
        return sidebar
    
    def create_user_header(self) -> QWidget:
        """Create user information header"""
        header = QFrame()
        header.setFrameStyle(QFrame.StyledPanel)
        header.setMaximumHeight(80)
        
        layout = QVBoxLayout(header)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # User name
        name_label = QLabel(self.user_data['username'])
        name_label.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['header_font_size'], QFont.Bold))
        name_label.setAlignment(Qt.AlignCenter)
        
        # User role
        role_label = QLabel(self.user_data['role'])
        role_label.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['font_size']))
        role_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(name_label)
        layout.addWidget(role_label)
        
        return header
    
    def create_content_area(self) -> QWidget:
        """Create main content area"""
        content_frame = QFrame()
        content_frame.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(content_frame)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Content header
        self.content_header = QLabel("مرحباً بك في نظام المحاسبة")
        self.content_header.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['title_font_size'], QFont.Bold))
        self.content_header.setAlignment(Qt.AlignCenter)
        self.content_header.setMaximumHeight(50)
        
        # Stacked widget for different modules
        self.stacked_widget = QStackedWidget()
        
        layout.addWidget(self.content_header)
        layout.addWidget(self.stacked_widget)
        
        return content_frame
    
    def setup_menu_bar(self):
        """Setup application menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("ملف")
        
        logout_action = QAction("تسجيل الخروج", self)
        logout_action.triggered.connect(self.handle_logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu("عرض")
        
        refresh_action = QAction("تحديث", self)
        refresh_action.triggered.connect(self.refresh_current_module)
        view_menu.addAction(refresh_action)
        
        # Help menu
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage(f"مرحباً {self.user_data['username']} - {self.user_data['role']}")
    
    def apply_styles(self):
        """Apply modern styles to the main window"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {UI_CONFIG['background_color']};
            }}
            
            QFrame {{
                background-color: white;
                border: 1px solid #ddd;
            }}
            
            QListWidget {{
                background-color: {UI_CONFIG['sidebar_color']};
                border: none;
                outline: none;
            }}
            
            QListWidget::item {{
                color: white;
                padding: 12px 15px;
                border-bottom: 1px solid #495057;
                font-size: {UI_CONFIG['font_size']}px;
            }}
            
            QListWidget::item:hover {{
                background-color: #495057;
            }}
            
            QListWidget::item:selected {{
                background-color: {UI_CONFIG['primary_color']};
                font-weight: bold;
            }}
            
            QPushButton {{
                background-color: {UI_CONFIG['danger_color']};
                color: white;
                border: none;
                padding: 10px;
                font-size: {UI_CONFIG['font_size']}px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: #c82333;
            }}
            
            QLabel {{
                color: {UI_CONFIG['text_color']};
            }}
            
            QMenuBar {{
                background-color: {UI_CONFIG['background_color']};
                border-bottom: 1px solid #ddd;
            }}
            
            QMenuBar::item {{
                padding: 5px 10px;
            }}
            
            QMenuBar::item:selected {{
                background-color: {UI_CONFIG['primary_color']};
                color: white;
            }}
            
            QStatusBar {{
                background-color: {UI_CONFIG['background_color']};
                border-top: 1px solid #ddd;
            }}
        """)
    
    def load_user_permissions(self):
        """Load navigation items based on user permissions"""
        user_role = self.user_data['role']
        
        for item in self.navigation_items:
            if check_permission(user_role, item.permission):
                list_item = QListWidgetItem(f"{item.icon} {item.title}")
                list_item.setData(Qt.UserRole, item.name)
                self.nav_list.addItem(list_item)
    
    def on_navigation_clicked(self, item: QListWidgetItem):
        """Handle navigation item click"""
        module_name = item.data(Qt.UserRole)
        self.show_module(module_name)
    
    def show_module(self, module_name: str):
        """Show specified module"""
        if module_name == self.current_module:
            return
        
        self.current_module = module_name
        
        # Update content header
        module_title = next((item.title for item in self.navigation_items 
                           if item.name == module_name), module_name)
        self.content_header.setText(module_title)
        
        # Get or create module widget
        if module_name not in self.module_widgets:
            self.module_widgets[module_name] = self.create_module_widget(module_name)
            self.stacked_widget.addWidget(self.module_widgets[module_name])
        
        # Show module widget
        self.stacked_widget.setCurrentWidget(self.module_widgets[module_name])
        
        # Update status bar
        self.status_bar.showMessage(f"الوحدة الحالية: {module_title}")
        
        # Emit signal
        self.module_changed.emit(module_name)
    
    def create_module_widget(self, module_name: str) -> QWidget:
        """Create widget for specified module"""
        try:
            if module_name == "users":
                from ui.users_ui import UsersWidget
                return UsersWidget(self.user_data)
            elif module_name == "customers":
                from ui.customers_ui import CustomersWidget
                return CustomersWidget(self.user_data)
            elif module_name == "suppliers":
                from ui.suppliers_ui import SuppliersWidget
                return SuppliersWidget(self.user_data)
            elif module_name == "products":
                from ui.products_ui import ProductsWidget
                return ProductsWidget(self.user_data)
            elif module_name == "sales":
                from ui.sales_ui import SalesWidget
                return SalesWidget(self.user_data)
            elif module_name == "purchases":
                from ui.purchases_ui import PurchasesWidget
                return PurchasesWidget(self.user_data)
            elif module_name == "accounts":
                from ui.accounts_ui import AccountsWidget
                return AccountsWidget(self.user_data)
            elif module_name == "reports":
                from ui.reports_ui import ReportsWidget
                return ReportsWidget(self.user_data)
            elif module_name == "audit":
                from ui.audit_ui import AuditWidget
                return AuditWidget(self.user_data)
            else:
                # Dashboard or placeholder
                widget = QWidget()
                layout = QVBoxLayout(widget)

                if module_name == "dashboard":
                    from ui.dashboard_ui import DashboardWidget
                    return DashboardWidget(self.user_data)
                else:
                    label = QLabel(f"وحدة {module_name} قيد التطوير")
                    label.setAlignment(Qt.AlignCenter)
                    label.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['header_font_size']))
                    layout.addWidget(label)

                return widget
        except ImportError as e:
            logger.warning(f"Module {module_name} not found: {e}")
            # Return placeholder widget
            widget = QWidget()
            layout = QVBoxLayout(widget)

            label = QLabel(f"وحدة {module_name} قيد التطوير")
            label.setAlignment(Qt.AlignCenter)
            label.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['header_font_size']))

            layout.addWidget(label)
            return widget
    
    def refresh_current_module(self):
        """Refresh current module"""
        if self.current_module:
            # Emit refresh signal or call module refresh method
            self.status_bar.showMessage("تم تحديث البيانات", 2000)
    
    def handle_logout(self):
        """Handle user logout"""
        reply = QMessageBox.question(self, "تأكيد", "هل تريد تسجيل الخروج؟",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.user_logout.emit()
            self.close()
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "حول البرنامج", 
                         f"{APP_CONFIG['app_name']}\n"
                         f"الإصدار: {APP_CONFIG['version']}\n\n"
                         "نظام محاسبة شامل لإدارة العمليات المالية")
    
    def closeEvent(self, event):
        """Handle window close event"""
        reply = QMessageBox.question(self, "تأكيد الخروج", "هل تريد إغلاق البرنامج؟",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()

# Test the main window
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Mock user data for testing
    user_data = {
        'userid': 1,
        'username': 'admin',
        'role': 'مدير',
        'isactive': True
    }
    
    window = MainWindow(user_data)
    window.show()
    
    sys.exit(app.exec_())
