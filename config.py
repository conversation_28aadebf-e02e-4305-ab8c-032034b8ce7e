# -*- coding: utf-8 -*-
"""
Configuration file for the Accounting System Desktop Application
Contains database connection settings and application constants
"""

import os
from typing import Dict, Any

# Database Configuration
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'accounting_system',
    'user': 'accounting_user',
    'password': '0981135'
}

# Application Configuration
APP_CONFIG = {
    'app_name': 'نظام المحاسبة',
    'version': '1.0.0',
    'window_title': 'نظام المحاسبة - إدارة شاملة',
    'window_size': (1400, 900),
    'min_window_size': (1200, 700),
    'icon_path': 'assets/icon.png',  # Optional: Add application icon
}

# User Roles
USER_ROLES = {
    'مدير': ['users', 'customers', 'suppliers', 'products', 'sales', 'purchases', 'accounts', 'reports', 'audit'],
    'محاسب': ['customers', 'suppliers', 'products', 'sales', 'purchases', 'accounts', 'reports'],
    'بائع': ['customers', 'products', 'sales', 'reports']
}

# UI Configuration
UI_CONFIG = {
    'font_family': 'Arial',
    'font_size': 10,
    'header_font_size': 12,
    'title_font_size': 14,
    'rtl_support': True,
    'theme': 'modern',
    'primary_color': '#2E86AB',
    'secondary_color': '#A23B72',
    'success_color': '#28A745',
    'warning_color': '#FFC107',
    'danger_color': '#DC3545',
    'background_color': '#F8F9FA',
    'sidebar_color': '#343A40',
    'text_color': '#212529'
}

# Table Configuration
TABLE_CONFIG = {
    'rows_per_page': 50,
    'max_rows_display': 1000,
    'auto_resize_columns': True,
    'alternating_row_colors': True,
    'show_grid': True
}

# Export Configuration
EXPORT_CONFIG = {
    'excel_format': 'xlsx',
    'pdf_page_size': 'A4',
    'pdf_orientation': 'portrait',
    'default_export_path': os.path.expanduser('~/Desktop/Accounting_Reports'),
    'date_format': '%Y-%m-%d',
    'datetime_format': '%Y-%m-%d %H:%M:%S'
}

# Validation Rules
VALIDATION_RULES = {
    'username': {
        'min_length': 3,
        'max_length': 50,
        'pattern': r'^[a-zA-Z0-9_]+$'
    },
    'password': {
        'min_length': 6,
        'max_length': 255
    },
    'phone': {
        'pattern': r'^[0-9+\-\s()]+$',
        'max_length': 20
    },
    'name': {
        'min_length': 2,
        'max_length': 100
    },
    'price': {
        'min_value': 0,
        'max_value': *********.99,
        'decimal_places': 2
    },
    'quantity': {
        'min_value': 0,
        'max_value': *********.99,
        'decimal_places': 2
    }
}

# Messages (Arabic)
MESSAGES = {
    'success': {
        'save': 'تم الحفظ بنجاح',
        'update': 'تم التحديث بنجاح',
        'delete': 'تم الحذف بنجاح',
        'login': 'تم تسجيل الدخول بنجاح',
        'export': 'تم تصدير التقرير بنجاح'
    },
    'error': {
        'connection': 'خطأ في الاتصال بقاعدة البيانات',
        'login': 'اسم المستخدم أو كلمة المرور غير صحيحة',
        'permission': 'ليس لديك صلاحية للوصول لهذه الوظيفة',
        'validation': 'يرجى التحقق من البيانات المدخلة',
        'duplicate': 'البيانات موجودة مسبقاً',
        'not_found': 'البيانات غير موجودة',
        'delete_constraint': 'لا يمكن حذف هذا العنصر لوجود بيانات مرتبطة به'
    },
    'warning': {
        'unsaved_changes': 'يوجد تغييرات غير محفوظة، هل تريد المتابعة؟',
        'delete_confirm': 'هل أنت متأكد من حذف هذا العنصر؟',
        'low_stock': 'تحذير: كمية المنتج أقل من الحد الأدنى'
    },
    'info': {
        'loading': 'جاري التحميل...',
        'processing': 'جاري المعالجة...',
        'no_data': 'لا توجد بيانات للعرض',
        'search_placeholder': 'البحث...'
    }
}

# Report Templates
REPORT_TEMPLATES = {
    'daily_sales': {
        'title': 'تقرير المبيعات اليومية',
        'columns': ['رقم الفاتورة', 'العميل', 'التاريخ', 'المبلغ الإجمالي', 'المبلغ المدفوع', 'المتبقي']
    },
    'monthly_sales': {
        'title': 'تقرير المبيعات الشهرية',
        'columns': ['الشهر', 'عدد الفواتير', 'إجمالي المبيعات', 'إجمالي المدفوع', 'إجمالي المتبقي']
    },
    'customer_balances': {
        'title': 'تقرير أرصدة العملاء',
        'columns': ['رقم العميل', 'اسم العميل', 'الهاتف', 'الرصيد']
    },
    'low_stock': {
        'title': 'تقرير المنتجات تحت الحد الأدنى',
        'columns': ['رقم المنتج', 'اسم المنتج', 'الفئة', 'الكمية الحالية', 'الحد الأدنى']
    },
    'supplier_purchases': {
        'title': 'تقرير المشتريات حسب المورد',
        'columns': ['المورد', 'عدد الفواتير', 'إجمالي المشتريات', 'إجمالي المدفوع', 'المتبقي']
    }
}

def get_database_url() -> str:
    """Generate database connection URL"""
    return f"postgresql://{DATABASE_CONFIG['user']}:{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"

def create_export_directory() -> str:
    """Create export directory if it doesn't exist"""
    export_path = EXPORT_CONFIG['default_export_path']
    os.makedirs(export_path, exist_ok=True)
    return export_path

def get_user_permissions(role: str) -> list:
    """Get user permissions based on role"""
    return USER_ROLES.get(role, [])

def validate_field(field_name: str, value: Any) -> tuple[bool, str]:
    """Validate field value based on validation rules"""
    if field_name not in VALIDATION_RULES:
        return True, ""
    
    rules = VALIDATION_RULES[field_name]
    
    # Check string length
    if isinstance(value, str):
        if 'min_length' in rules and len(value) < rules['min_length']:
            return False, f"الحد الأدنى للطول {rules['min_length']} أحرف"
        if 'max_length' in rules and len(value) > rules['max_length']:
            return False, f"الحد الأقصى للطول {rules['max_length']} حرف"
        if 'pattern' in rules:
            import re
            if not re.match(rules['pattern'], value):
                return False, "تنسيق البيانات غير صحيح"
    
    # Check numeric values
    if isinstance(value, (int, float)):
        if 'min_value' in rules and value < rules['min_value']:
            return False, f"الحد الأدنى للقيمة {rules['min_value']}"
        if 'max_value' in rules and value > rules['max_value']:
            return False, f"الحد الأقصى للقيمة {rules['max_value']}"
    
    return True, ""
