# -*- coding: utf-8 -*-
"""
Enhanced error handling module for database operations
Provides user-friendly error messages and recovery mechanisms
"""

import logging
import psycopg2
from typing import Dict, Any, Optional, Tuple
from config import MESSAGES

logger = logging.getLogger(__name__)

class DatabaseError(Exception):
    """Custom database error with Arabic message support"""
    
    def __init__(self, message: str, arabic_message: str = None, error_code: str = None):
        self.message = message
        self.arabic_message = arabic_message or message
        self.error_code = error_code
        super().__init__(self.message)

class DatabaseErrorHandler:
    """Centralized database error handling"""
    
    @staticmethod
    def handle_psycopg2_error(error: Exception) -> DatabaseError:
        """Convert psycopg2 errors to user-friendly messages"""
        
        if isinstance(error, psycopg2.OperationalError):
            error_msg = str(error).lower()
            
            if 'connection' in error_msg or 'server' in error_msg:
                return DatabaseError(
                    "Database connection failed",
                    MESSAGES['error']['connection'],
                    "CONNECTION_ERROR"
                )
            elif 'timeout' in error_msg:
                return DatabaseError(
                    "Database operation timed out",
                    "انتهت مهلة العملية، يرجى المحاولة مرة أخرى",
                    "TIMEOUT_ERROR"
                )
            elif 'authentication' in error_msg or 'password' in error_msg:
                return DatabaseError(
                    "Database authentication failed",
                    "فشل في المصادقة مع قاعدة البيانات",
                    "AUTH_ERROR"
                )
            else:
                return DatabaseError(
                    f"Database operational error: {error}",
                    "خطأ في تشغيل قاعدة البيانات",
                    "OPERATIONAL_ERROR"
                )
        
        elif isinstance(error, psycopg2.IntegrityError):
            error_msg = str(error).lower()
            
            if 'unique' in error_msg or 'duplicate' in error_msg:
                return DatabaseError(
                    "Duplicate data entry",
                    MESSAGES['error']['duplicate'],
                    "DUPLICATE_ERROR"
                )
            elif 'foreign key' in error_msg:
                return DatabaseError(
                    "Cannot delete: related data exists",
                    MESSAGES['error']['delete_constraint'],
                    "FOREIGN_KEY_ERROR"
                )
            elif 'check constraint' in error_msg:
                return DatabaseError(
                    "Data validation failed",
                    MESSAGES['error']['validation'],
                    "VALIDATION_ERROR"
                )
            else:
                return DatabaseError(
                    f"Data integrity error: {error}",
                    "خطأ في سلامة البيانات",
                    "INTEGRITY_ERROR"
                )
        
        elif isinstance(error, psycopg2.DataError):
            return DatabaseError(
                "Invalid data format",
                "تنسيق البيانات غير صحيح",
                "DATA_FORMAT_ERROR"
            )
        
        elif isinstance(error, psycopg2.ProgrammingError):
            error_msg = str(error).lower()
            
            if 'relation' in error_msg and 'does not exist' in error_msg:
                return DatabaseError(
                    "Database table not found",
                    "جدول قاعدة البيانات غير موجود",
                    "TABLE_NOT_FOUND"
                )
            elif 'column' in error_msg and 'does not exist' in error_msg:
                return DatabaseError(
                    "Database column not found",
                    "عمود قاعدة البيانات غير موجود",
                    "COLUMN_NOT_FOUND"
                )
            else:
                return DatabaseError(
                    f"Database programming error: {error}",
                    "خطأ في برمجة قاعدة البيانات",
                    "PROGRAMMING_ERROR"
                )
        
        else:
            return DatabaseError(
                f"Unknown database error: {error}",
                "خطأ غير معروف في قاعدة البيانات",
                "UNKNOWN_ERROR"
            )
    
    @staticmethod
    def handle_connection_error(error: Exception) -> DatabaseError:
        """Handle connection-specific errors"""
        return DatabaseError(
            f"Connection error: {error}",
            MESSAGES['error']['connection'],
            "CONNECTION_ERROR"
        )
    
    @staticmethod
    def get_recovery_suggestions(error_code: str) -> Dict[str, Any]:
        """Get recovery suggestions for specific error codes"""
        suggestions = {
            "CONNECTION_ERROR": {
                "arabic": "تحقق من تشغيل PostgreSQL وصحة إعدادات الاتصال",
                "english": "Check if PostgreSQL is running and connection settings are correct",
                "actions": [
                    "Check PostgreSQL service status",
                    "Verify database connection parameters",
                    "Test network connectivity"
                ]
            },
            "AUTH_ERROR": {
                "arabic": "تحقق من اسم المستخدم وكلمة المرور لقاعدة البيانات",
                "english": "Check database username and password",
                "actions": [
                    "Verify database credentials",
                    "Check user permissions",
                    "Reset database password if needed"
                ]
            },
            "DUPLICATE_ERROR": {
                "arabic": "البيانات موجودة مسبقاً، يرجى استخدام قيم مختلفة",
                "english": "Data already exists, please use different values",
                "actions": [
                    "Check for existing records",
                    "Use unique values",
                    "Update existing record instead"
                ]
            },
            "FOREIGN_KEY_ERROR": {
                "arabic": "لا يمكن حذف هذا العنصر لوجود بيانات مرتبطة به",
                "english": "Cannot delete: related data exists",
                "actions": [
                    "Delete related records first",
                    "Use soft delete instead",
                    "Check data relationships"
                ]
            },
            "TABLE_NOT_FOUND": {
                "arabic": "قاعدة البيانات غير مكتملة، يرجى تشغيل سكريبت التثبيت",
                "english": "Database incomplete, please run installation script",
                "actions": [
                    "Run database installation script",
                    "Check database schema",
                    "Verify table creation"
                ]
            }
        }
        
        return suggestions.get(error_code, {
            "arabic": "خطأ غير معروف، يرجى مراجعة سجل الأخطاء",
            "english": "Unknown error, please check error logs",
            "actions": ["Check application logs", "Contact support"]
        })

def safe_execute(func, *args, **kwargs) -> Tuple[bool, Any, Optional[DatabaseError]]:
    """
    Safely execute a database function with error handling
    Returns: (success, result, error)
    """
    try:
        result = func(*args, **kwargs)
        return True, result, None
    except psycopg2.Error as e:
        error = DatabaseErrorHandler.handle_psycopg2_error(e)
        logger.error(f"Database error in {func.__name__}: {error.message}")
        return False, None, error
    except ConnectionError as e:
        error = DatabaseErrorHandler.handle_connection_error(e)
        logger.error(f"Connection error in {func.__name__}: {error.message}")
        return False, None, error
    except Exception as e:
        error = DatabaseError(
            f"Unexpected error in {func.__name__}: {e}",
            "خطأ غير متوقع",
            "UNEXPECTED_ERROR"
        )
        logger.error(f"Unexpected error in {func.__name__}: {e}")
        return False, None, error

def log_database_operation(operation: str, table: str, success: bool, error: str = None):
    """Log database operations for debugging"""
    if success:
        logger.info(f"Database operation successful: {operation} on {table}")
    else:
        logger.error(f"Database operation failed: {operation} on {table} - {error}")

# Decorator for automatic error handling
def handle_db_errors(func):
    """Decorator to automatically handle database errors"""
    def wrapper(*args, **kwargs):
        success, result, error = safe_execute(func, *args, **kwargs)
        if not success:
            raise error
        return result
    return wrapper
