# -*- coding: utf-8 -*-
"""
Database maintenance and optimization module
Provides tools for database health monitoring and performance optimization
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from .connection import db_manager
from .error_handler import safe_execute, DatabaseError

logger = logging.getLogger(__name__)

class DatabaseMaintenance:
    """Database maintenance and optimization tools"""
    
    def __init__(self):
        self.db_manager = db_manager
    
    def vacuum_analyze_tables(self) -> Dict[str, Any]:
        """Perform VACUUM ANALYZE on all tables for performance optimization"""
        results = {
            'success': False,
            'tables_processed': [],
            'errors': []
        }
        
        try:
            # Get all user tables
            tables = self.get_user_tables()
            
            with self.db_manager.get_connection() as conn:
                conn.autocommit = True  # Required for VACUUM
                with conn.cursor() as cursor:
                    for table in tables:
                        try:
                            cursor.execute(f"VACUUM ANALYZE {table}")
                            results['tables_processed'].append(table)
                            logger.info(f"VACUUM ANALYZE completed for table: {table}")
                        except Exception as e:
                            error_msg = f"Failed to vacuum table {table}: {e}"
                            results['errors'].append(error_msg)
                            logger.error(error_msg)
                
                conn.autocommit = False  # Reset autocommit
            
            results['success'] = len(results['errors']) == 0
            
        except Exception as e:
            results['errors'].append(f"VACUUM operation failed: {e}")
            logger.error(f"VACUUM operation failed: {e}")
        
        return results
    
    def get_user_tables(self) -> List[str]:
        """Get list of user tables in the database"""
        try:
            query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            """
            
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query)
                    return [row['table_name'] for row in cursor.fetchall()]
        
        except Exception as e:
            logger.error(f"Failed to get user tables: {e}")
            return []
    
    def get_table_statistics(self) -> Dict[str, Any]:
        """Get comprehensive table statistics"""
        stats = {
            'tables': {},
            'total_size': 0,
            'total_rows': 0
        }
        
        try:
            query = """
                SELECT 
                    schemaname,
                    tablename,
                    attname,
                    n_distinct,
                    correlation
                FROM pg_stats 
                WHERE schemaname = 'public'
                ORDER BY tablename, attname
            """
            
            # Get table sizes and row counts
            size_query = """
                SELECT 
                    t.table_name,
                    pg_size_pretty(pg_total_relation_size(c.oid)) as size,
                    pg_total_relation_size(c.oid) as size_bytes,
                    (SELECT COUNT(*) FROM information_schema.columns 
                     WHERE table_name = t.table_name AND table_schema = 'public') as column_count
                FROM information_schema.tables t
                JOIN pg_class c ON c.relname = t.table_name
                WHERE t.table_schema = 'public' 
                AND t.table_type = 'BASE TABLE'
                ORDER BY pg_total_relation_size(c.oid) DESC
            """
            
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Get table sizes
                    cursor.execute(size_query)
                    for row in cursor.fetchall():
                        table_name = row['table_name']
                        stats['tables'][table_name] = {
                            'size': row['size'],
                            'size_bytes': row['size_bytes'],
                            'column_count': row['column_count'],
                            'row_count': 0  # Will be filled later
                        }
                        stats['total_size'] += row['size_bytes']
                    
                    # Get row counts for each table
                    for table_name in stats['tables'].keys():
                        try:
                            cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                            row_count = cursor.fetchone()['count']
                            stats['tables'][table_name]['row_count'] = row_count
                            stats['total_rows'] += row_count
                        except Exception as e:
                            logger.warning(f"Failed to get row count for {table_name}: {e}")
                            stats['tables'][table_name]['row_count'] = -1
        
        except Exception as e:
            logger.error(f"Failed to get table statistics: {e}")
        
        return stats
    
    def check_database_performance(self) -> Dict[str, Any]:
        """Check database performance metrics"""
        performance = {
            'connection_count': 0,
            'active_queries': 0,
            'slow_queries': [],
            'index_usage': {},
            'recommendations': []
        }
        
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Get connection count
                    cursor.execute("""
                        SELECT COUNT(*) as count 
                        FROM pg_stat_activity 
                        WHERE datname = current_database()
                    """)
                    performance['connection_count'] = cursor.fetchone()['count']
                    
                    # Get active queries
                    cursor.execute("""
                        SELECT COUNT(*) as count 
                        FROM pg_stat_activity 
                        WHERE datname = current_database() 
                        AND state = 'active'
                        AND query NOT LIKE '%pg_stat_activity%'
                    """)
                    performance['active_queries'] = cursor.fetchone()['count']
                    
                    # Check for tables without primary keys
                    cursor.execute("""
                        SELECT table_name
                        FROM information_schema.tables t
                        WHERE table_schema = 'public'
                        AND table_type = 'BASE TABLE'
                        AND NOT EXISTS (
                            SELECT 1
                            FROM information_schema.table_constraints tc
                            WHERE tc.table_name = t.table_name
                            AND tc.table_schema = 'public'
                            AND tc.constraint_type = 'PRIMARY KEY'
                        )
                    """)
                    
                    tables_without_pk = [row['table_name'] for row in cursor.fetchall()]
                    if tables_without_pk:
                        performance['recommendations'].append({
                            'type': 'missing_primary_key',
                            'message': f"Tables without primary keys: {', '.join(tables_without_pk)}",
                            'arabic': f"جداول بدون مفاتيح أساسية: {', '.join(tables_without_pk)}"
                        })
        
        except Exception as e:
            logger.error(f"Failed to check database performance: {e}")
        
        return performance
    
    def cleanup_old_audit_logs(self, days_to_keep: int = 90) -> Dict[str, Any]:
        """Clean up old audit log entries"""
        result = {
            'success': False,
            'deleted_count': 0,
            'error': None
        }
        
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        DELETE FROM audit_log 
                        WHERE timestamp < %s
                    """, (cutoff_date,))
                    
                    result['deleted_count'] = cursor.rowcount
                    conn.commit()
                    result['success'] = True
                    
                    logger.info(f"Cleaned up {result['deleted_count']} old audit log entries")
        
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Failed to cleanup audit logs: {e}")
        
        return result
    
    def backup_database_schema(self) -> Dict[str, Any]:
        """Generate database schema backup information"""
        schema_info = {
            'tables': {},
            'indexes': {},
            'constraints': {},
            'functions': {}
        }
        
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Get table information
                    cursor.execute("""
                        SELECT 
                            table_name,
                            column_name,
                            data_type,
                            is_nullable,
                            column_default
                        FROM information_schema.columns
                        WHERE table_schema = 'public'
                        ORDER BY table_name, ordinal_position
                    """)
                    
                    for row in cursor.fetchall():
                        table_name = row['table_name']
                        if table_name not in schema_info['tables']:
                            schema_info['tables'][table_name] = []
                        
                        schema_info['tables'][table_name].append({
                            'column': row['column_name'],
                            'type': row['data_type'],
                            'nullable': row['is_nullable'],
                            'default': row['column_default']
                        })
                    
                    # Get indexes
                    cursor.execute("""
                        SELECT 
                            schemaname,
                            tablename,
                            indexname,
                            indexdef
                        FROM pg_indexes
                        WHERE schemaname = 'public'
                        ORDER BY tablename, indexname
                    """)
                    
                    for row in cursor.fetchall():
                        table_name = row['tablename']
                        if table_name not in schema_info['indexes']:
                            schema_info['indexes'][table_name] = []
                        
                        schema_info['indexes'][table_name].append({
                            'name': row['indexname'],
                            'definition': row['indexdef']
                        })
        
        except Exception as e:
            logger.error(f"Failed to backup database schema: {e}")
        
        return schema_info
    
    def generate_maintenance_report(self) -> Dict[str, Any]:
        """Generate comprehensive maintenance report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'database_health': self.db_manager.check_database_health(),
            'table_statistics': self.get_table_statistics(),
            'performance_metrics': self.check_database_performance(),
            'recommendations': []
        }
        
        # Add recommendations based on analysis
        if report['table_statistics']['total_rows'] > 100000:
            report['recommendations'].append({
                'type': 'performance',
                'message': 'Consider running VACUUM ANALYZE for better performance',
                'arabic': 'يُنصح بتشغيل VACUUM ANALYZE لتحسين الأداء'
            })
        
        if report['performance_metrics']['connection_count'] > 15:
            report['recommendations'].append({
                'type': 'connections',
                'message': 'High number of database connections detected',
                'arabic': 'تم اكتشاف عدد كبير من اتصالات قاعدة البيانات'
            })
        
        return report
