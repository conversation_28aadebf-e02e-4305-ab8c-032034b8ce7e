# -*- coding: utf-8 -*-
"""
Products management UI module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
                            QHeaderView, QGroupBox, QSpinBox, QDoubleSpinBox,
                            QComboBox, QCheckBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from db.repositories import ProductsRepository
from config import UI_CONFIG, MESSAGES
import logging

logger = logging.getLogger(__name__)

class ProductDialog(QDialog):
    """Dialog for adding/editing products"""
    
    def __init__(self, product_data=None, parent=None):
        super().__init__(parent)
        self.product_data = product_data
        self.is_edit_mode = product_data is not None
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_product_data()
    
    def setup_ui(self):
        """Setup dialog UI"""
        title = "تعديل منتج" if self.is_edit_mode else "إضافة منتج جديد"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # Form
        form_group = QGroupBox("بيانات المنتج")
        form_layout = QFormLayout(form_group)
        
        # Name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم المنتج")
        form_layout.addRow("اسم المنتج:", self.name_edit)
        
        # Category
        self.category_edit = QLineEdit()
        self.category_edit.setPlaceholderText("أدخل فئة المنتج")
        form_layout.addRow("الفئة:", self.category_edit)
        
        # Unit
        self.unit_edit = QLineEdit()
        self.unit_edit.setPlaceholderText("مثل: قطعة، كيلو، متر")
        form_layout.addRow("الوحدة:", self.unit_edit)
        
        # Cost Price
        self.cost_price_spin = QDoubleSpinBox()
        self.cost_price_spin.setRange(0, 999999999.99)
        self.cost_price_spin.setDecimals(2)
        form_layout.addRow("سعر التكلفة:", self.cost_price_spin)
        
        # Sale Price
        self.sale_price_spin = QDoubleSpinBox()
        self.sale_price_spin.setRange(0, 999999999.99)
        self.sale_price_spin.setDecimals(2)
        form_layout.addRow("سعر البيع:", self.sale_price_spin)
        
        # Quantity
        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setRange(0, 999999999.99)
        self.quantity_spin.setDecimals(2)
        form_layout.addRow("الكمية:", self.quantity_spin)
        
        # Reorder Level
        self.reorder_spin = QSpinBox()
        self.reorder_spin.setRange(0, 999999999)
        form_layout.addRow("الحد الأدنى:", self.reorder_spin)
        
        # Active status (for edit mode)
        if self.is_edit_mode:
            self.active_checkbox = QCheckBox("المنتج نشط")
            form_layout.addRow("الحالة:", self.active_checkbox)
        
        layout.addWidget(form_group)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.button(QDialogButtonBox.Ok).setText("حفظ")
        button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
        self.button_box = button_box
    
    def setup_connections(self):
        """Setup signal connections"""
        self.button_box.accepted.connect(self.validate_and_accept)
        self.button_box.rejected.connect(self.reject)
    
    def load_product_data(self):
        """Load product data for editing"""
        if self.product_data:
            self.name_edit.setText(self.product_data.get('name', ''))
            self.category_edit.setText(self.product_data.get('category', ''))
            self.unit_edit.setText(self.product_data.get('unit', ''))
            self.cost_price_spin.setValue(float(self.product_data.get('costprice', 0)))
            self.sale_price_spin.setValue(float(self.product_data.get('saleprice', 0)))
            self.quantity_spin.setValue(float(self.product_data.get('quantity', 0)))
            self.reorder_spin.setValue(int(self.product_data.get('reorderlevel', 0)))
            
            if hasattr(self, 'active_checkbox'):
                self.active_checkbox.setChecked(self.product_data.get('isactive', True))
    
    def validate_and_accept(self):
        """Validate form data and accept dialog"""
        name = self.name_edit.text().strip()
        category = self.category_edit.text().strip()
        unit = self.unit_edit.text().strip()
        
        if not name:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المنتج")
            return
        
        if not category:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال فئة المنتج")
            return
        
        if not unit:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال وحدة المنتج")
            return
        
        if self.sale_price_spin.value() < self.cost_price_spin.value():
            QMessageBox.warning(self, "خطأ", "سعر البيع يجب أن يكون أكبر من أو يساوي سعر التكلفة")
            return
        
        self.accept()
    
    def get_form_data(self):
        """Get form data"""
        data = {
            'Name': self.name_edit.text().strip(),
            'Category': self.category_edit.text().strip(),
            'Unit': self.unit_edit.text().strip(),
            'CostPrice': self.cost_price_spin.value(),
            'SalePrice': self.sale_price_spin.value(),
            'Quantity': self.quantity_spin.value(),
            'ReorderLevel': self.reorder_spin.value()
        }
        
        if self.is_edit_mode and hasattr(self, 'active_checkbox'):
            data['IsActive'] = self.active_checkbox.isChecked()
        
        return data

class ProductsWidget(QWidget):
    """Main products management widget"""
    
    def __init__(self, current_user_data, parent=None):
        super().__init__(parent)
        self.current_user_data = current_user_data
        self.products_repo = ProductsRepository()
        self.setup_ui()
        self.setup_connections()
        self.load_products()
    
    def setup_ui(self):
        """Setup widget UI"""
        layout = QVBoxLayout(self)
        
        # Toolbar
        toolbar_layout = QHBoxLayout()
        
        self.add_button = QPushButton("إضافة منتج")
        self.edit_button = QPushButton("تعديل")
        self.delete_button = QPushButton("حذف")
        self.refresh_button = QPushButton("تحديث")
        self.low_stock_button = QPushButton("نواقص المخزون")
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في المنتجات...")
        self.search_button = QPushButton("بحث")
        
        toolbar_layout.addWidget(self.add_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addWidget(self.delete_button)
        toolbar_layout.addWidget(self.low_stock_button)
        toolbar_layout.addWidget(self.refresh_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(QLabel("البحث:"))
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(self.search_button)
        
        # Table
        self.table = QTableWidget()
        self.table.setColumnCount(9)
        self.table.setHorizontalHeaderLabels([
            "رقم المنتج", "اسم المنتج", "الفئة", "الوحدة", "سعر التكلفة", 
            "سعر البيع", "الكمية", "الحد الأدنى", "الحالة"
        ])
        
        # Configure table
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        
        layout.addLayout(toolbar_layout)
        layout.addWidget(self.table)
        
        # Apply styles
        self.apply_styles()
    
    def setup_connections(self):
        """Setup signal connections"""
        self.add_button.clicked.connect(self.add_product)
        self.edit_button.clicked.connect(self.edit_product)
        self.delete_button.clicked.connect(self.delete_product)
        self.refresh_button.clicked.connect(self.load_products)
        self.low_stock_button.clicked.connect(self.show_low_stock)
        self.search_button.clicked.connect(self.search_products)
        self.search_edit.returnPressed.connect(self.search_products)
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        self.table.itemDoubleClicked.connect(self.edit_product)
    
    def apply_styles(self):
        """Apply styles to the widget"""
        self.setStyleSheet(f"""
            QPushButton {{
                padding: 8px 16px;
                margin: 2px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{ opacity: 0.8; }}
            QPushButton#add {{ background-color: {UI_CONFIG['success_color']}; color: white; }}
            QPushButton#edit {{ background-color: {UI_CONFIG['primary_color']}; color: white; }}
            QPushButton#delete {{ background-color: {UI_CONFIG['danger_color']}; color: white; }}
            QPushButton#low_stock {{ background-color: {UI_CONFIG['warning_color']}; color: white; }}
            QPushButton#refresh {{ background-color: #6c757d; color: white; }}
            QLineEdit {{ padding: 6px; border: 1px solid #ddd; border-radius: 4px; }}
            QTableWidget {{ gridline-color: #ddd; background-color: white; alternate-background-color: #f8f9fa; }}
            QTableWidget::item {{ padding: 8px; }}
            QHeaderView::section {{ background-color: {UI_CONFIG['primary_color']}; color: white; padding: 8px; border: none; font-weight: bold; }}
        """)
        
        self.add_button.setObjectName("add")
        self.edit_button.setObjectName("edit")
        self.delete_button.setObjectName("delete")
        self.low_stock_button.setObjectName("low_stock")
        self.refresh_button.setObjectName("refresh")
    
    def load_products(self):
        """Load products from database"""
        try:
            products = self.products_repo.get_active_products()
            self.populate_table(products)
        except Exception as e:
            logger.error(f"Error loading products: {e}")
            QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def populate_table(self, products):
        """Populate table with product data"""
        self.table.setRowCount(len(products))
        
        for row, product in enumerate(products):
            self.table.setItem(row, 0, QTableWidgetItem(str(product['productid'])))
            self.table.setItem(row, 1, QTableWidgetItem(product['name']))
            self.table.setItem(row, 2, QTableWidgetItem(product['category']))
            self.table.setItem(row, 3, QTableWidgetItem(product['unit']))
            self.table.setItem(row, 4, QTableWidgetItem(f"{float(product['costprice']):.2f}"))
            self.table.setItem(row, 5, QTableWidgetItem(f"{float(product['saleprice']):.2f}"))
            
            # Quantity with low stock warning
            quantity = float(product['quantity'])
            reorder_level = int(product['reorderlevel'])
            quantity_item = QTableWidgetItem(f"{quantity:.2f}")
            if quantity <= reorder_level and reorder_level > 0:
                quantity_item.setBackground(Qt.yellow)
            self.table.setItem(row, 6, quantity_item)
            
            self.table.setItem(row, 7, QTableWidgetItem(str(reorder_level)))
            
            # Status
            status = "نشط" if product.get('isactive', True) else "غير نشط"
            status_item = QTableWidgetItem(status)
            if not product.get('isactive', True):
                status_item.setBackground(Qt.lightGray)
            self.table.setItem(row, 8, status_item)
            
            # Store product data in first column
            self.table.item(row, 0).setData(Qt.UserRole, product)
    
    def on_selection_changed(self):
        """Handle table selection change"""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
    
    def get_selected_product(self):
        """Get selected product data"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            return self.table.item(current_row, 0).data(Qt.UserRole)
        return None
    
    def add_product(self):
        """Add new product"""
        dialog = ProductDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                form_data = dialog.get_form_data()
                self.products_repo.insert(form_data)
                QMessageBox.information(self, "نجح", MESSAGES['success']['save'])
                self.load_products()
            except Exception as e:
                logger.error(f"Error adding product: {e}")
                QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def edit_product(self):
        """Edit selected product"""
        product_data = self.get_selected_product()
        if not product_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للتعديل")
            return
        
        dialog = ProductDialog(product_data, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                form_data = dialog.get_form_data()
                self.products_repo.update('ProductID', product_data['productid'], form_data)
                QMessageBox.information(self, "نجح", MESSAGES['success']['update'])
                self.load_products()
            except Exception as e:
                logger.error(f"Error updating product: {e}")
                QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def delete_product(self):
        """Delete selected product"""
        product_data = self.get_selected_product()
        if not product_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد", 
                                   f"هل تريد حذف المنتج '{product_data['name']}'؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                self.products_repo.soft_delete('ProductID', product_data['productid'])
                QMessageBox.information(self, "نجح", "تم حذف المنتج")
                self.load_products()
            except Exception as e:
                logger.error(f"Error deleting product: {e}")
                QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def show_low_stock(self):
        """Show products with low stock"""
        try:
            products = self.products_repo.get_low_stock_products()
            self.populate_table(products)
            if not products:
                QMessageBox.information(self, "معلومات", "لا توجد منتجات تحت الحد الأدنى")
        except Exception as e:
            logger.error(f"Error loading low stock products: {e}")
            QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def search_products(self):
        """Search products"""
        search_term = self.search_edit.text().strip()
        if not search_term:
            self.load_products()
            return
        
        try:
            products = self.products_repo.search(['Name', 'Category'], search_term)
            self.populate_table(products)
        except Exception as e:
            logger.error(f"Error searching products: {e}")
            QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
