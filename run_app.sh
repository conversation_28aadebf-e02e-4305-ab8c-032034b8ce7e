#!/bin/bash

echo "Starting Accounting System..."
echo "نظام المحاسبة - جاري التشغيل..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed"
    echo "يرجى تثبيت Python 3 أولاً"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    echo "إنشاء البيئة الافتراضية..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install requirements
echo "Installing requirements..."
echo "تثبيت المتطلبات..."
pip install -r requirements.txt

# Run the application
echo "Starting application..."
echo "تشغيل التطبيق..."
python main.py
