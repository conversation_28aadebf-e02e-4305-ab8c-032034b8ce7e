-- =============================================================
-- Accounting System - Full Install Script
-- =============================================================
-- تعليمات التنفيذ (هام جداً):
-- 1) افتح جلسة psql كمستخدم لديه صلاحية إنشاء قواعد بيانات (مثلاً: postgres).
-- 2) شغّل PART A (ينشئ الدور والقاعدة) مرة واحدة فقط.
-- 3) بعد تشغيل PART A، اتصل بقاعدة accounting_system (داخل psql:  \c accounting_system) ثم شغّل PART B داخل تلك القاعدة.
-- 4) لا تستخدم \c داخل سكربت عند تشغيله عبر أدوات لا تدعم أوامر psql الخاصة.
-- 5) تأكد من تغيير كلمة مرور الحساب account_user/accounting_user بعد الإنشاء.
-- =============================================================

-- ======================
-- PART A: إنشاء الدور والقاعدة
-- شغّل هذا الجزء على قاعدة إدارية (مثل "postgres")
-- ======================
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_catalog.pg_roles WHERE rolname = 'accounting_user') THEN
        EXECUTE 'CREATE ROLE accounting_user LOGIN PASSWORD ''change_me_secure''';
    END IF;
END;
$$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_database WHERE datname = 'accounting_system') THEN
        EXECUTE 'CREATE DATABASE accounting_system WITH ENCODING = ''UTF8'' CONNECTION LIMIT = -1';
    END IF;
END;
$$;

-- ======================
-- PART B: إنشاء البنية داخل قاعدة accounting_system
-- بعد تشغيل PART A اتصل بقاعدة accounting_system ثم نفّذ الجزء التالي
-- (داخل psql استخدم: \c accounting_system)
-- ======================

-- Extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Tables
CREATE TABLE IF NOT EXISTS Users (
    UserID SERIAL PRIMARY KEY,
    Username VARCHAR(50) NOT NULL UNIQUE,
    PasswordHash VARCHAR(255) NOT NULL,
    Role VARCHAR(20) NOT NULL CHECK (Role IN ('مدير', 'محاسب', 'بائع')),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    IsActive BOOLEAN DEFAULT TRUE
);

CREATE TABLE IF NOT EXISTS Customers (
    CustomerID SERIAL PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Phone VARCHAR(20) NOT NULL,
    Address VARCHAR(200),
    Balance DECIMAL(18,2) DEFAULT 0.00,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    IsActive BOOLEAN DEFAULT TRUE,
    CONSTRAINT chk_balance CHECK (Balance >= -1000000 AND Balance <= 1000000)
);

CREATE TABLE IF NOT EXISTS Suppliers (
    SupplierID SERIAL PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Phone VARCHAR(20) NOT NULL,
    Address VARCHAR(200),
    Balance DECIMAL(18,2) DEFAULT 0.00,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    IsActive BOOLEAN DEFAULT TRUE,
    CONSTRAINT chk_supplier_balance CHECK (Balance >= -1000000 AND Balance <= 1000000)
);

CREATE TABLE IF NOT EXISTS Products (
    ProductID SERIAL PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Category VARCHAR(50) NOT NULL,
    Unit VARCHAR(20) NOT NULL,
    CostPrice DECIMAL(18,2) NOT NULL CHECK (CostPrice >= 0),
    SalePrice DECIMAL(18,2) NOT NULL CHECK (SalePrice >= 0),
    Quantity DECIMAL(18,2) DEFAULT 0 CHECK (Quantity >= 0),
    ReorderLevel INT DEFAULT 0 CHECK (ReorderLevel >= 0),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    IsActive BOOLEAN DEFAULT TRUE,
    CONSTRAINT chk_prices CHECK (SalePrice >= CostPrice)
);

CREATE TABLE IF NOT EXISTS SalesInvoices (
    InvoiceID SERIAL PRIMARY KEY,
    CustomerID INT NOT NULL,
    Date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    TotalAmount DECIMAL(18,2) DEFAULT 0.00 CHECK (TotalAmount >= 0),
    PaidAmount DECIMAL(18,2) DEFAULT 0.00 CHECK (PaidAmount >= 0),
    RemainingAmount DECIMAL(18,2) DEFAULT 0.00 CHECK (RemainingAmount >= 0),
    UserID INT NOT NULL,
    Status VARCHAR(20) DEFAULT 'مفتوحة' CHECK (Status IN ('مفتوحة', 'مدفوعة', 'ملغاة')),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_sales_customer FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
    CONSTRAINT fk_sales_user FOREIGN KEY (UserID) REFERENCES Users(UserID),
    CONSTRAINT chk_sales_amounts CHECK (TotalAmount = PaidAmount + RemainingAmount)
);

CREATE TABLE IF NOT EXISTS SalesInvoiceDetails (
    DetailID SERIAL PRIMARY KEY,
    InvoiceID INT NOT NULL,
    ProductID INT NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL CHECK (Quantity > 0),
    Price DECIMAL(18,2) NOT NULL CHECK (Price >= 0),
    Total DECIMAL(18,2) NOT NULL CHECK (Total >= 0),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_sales_detail_invoice FOREIGN KEY (InvoiceID) REFERENCES SalesInvoices(InvoiceID) ON DELETE CASCADE,
    CONSTRAINT fk_sales_detail_product FOREIGN KEY (ProductID) REFERENCES Products(ProductID),
    CONSTRAINT chk_sales_detail_total CHECK (Total = Quantity * Price)
);

CREATE TABLE IF NOT EXISTS PurchaseInvoices (
    InvoiceID SERIAL PRIMARY KEY,
    SupplierID INT NOT NULL,
    Date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    TotalAmount DECIMAL(18,2) DEFAULT 0.00 CHECK (TotalAmount >= 0),
    PaidAmount DECIMAL(18,2) DEFAULT 0.00 CHECK (PaidAmount >= 0),
    RemainingAmount DECIMAL(18,2) DEFAULT 0.00 CHECK (RemainingAmount >= 0),
    UserID INT NOT NULL,
    Status VARCHAR(20) DEFAULT 'مفتوحة' CHECK (Status IN ('مفتوحة', 'مدفوعة', 'ملغاة')),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_purchase_supplier FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
    CONSTRAINT fk_purchase_user FOREIGN KEY (UserID) REFERENCES Users(UserID),
    CONSTRAINT chk_purchase_amounts CHECK (TotalAmount = PaidAmount + RemainingAmount)
);

CREATE TABLE IF NOT EXISTS PurchaseInvoiceDetails (
    DetailID SERIAL PRIMARY KEY,
    InvoiceID INT NOT NULL,
    ProductID INT NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL CHECK (Quantity > 0),
    Price DECIMAL(18,2) NOT NULL CHECK (Price >= 0),
    Total DECIMAL(18,2) NOT NULL CHECK (Total >= 0),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_purchase_detail_invoice FOREIGN KEY (InvoiceID) REFERENCES PurchaseInvoices(InvoiceID) ON DELETE CASCADE,
    CONSTRAINT fk_purchase_detail_product FOREIGN KEY (ProductID) REFERENCES Products(ProductID),
    CONSTRAINT chk_purchase_detail_total CHECK (Total = Quantity * Price)
);

CREATE TABLE IF NOT EXISTS Accounts (
    AccountID SERIAL PRIMARY KEY,
    Name VARCHAR(100) NOT NULL UNIQUE,
    Type VARCHAR(50) NOT NULL CHECK (Type IN ('أصول', 'خصوم', 'إيرادات', 'مصاريف')),
    Balance DECIMAL(18,2) DEFAULT 0.00,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    IsActive BOOLEAN DEFAULT TRUE
);

CREATE TABLE IF NOT EXISTS JournalEntries (
    EntryID SERIAL PRIMARY KEY,
    Date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    Description VARCHAR(200) NOT NULL,
    Debit DECIMAL(18,2) NOT NULL CHECK (Debit >= 0),
    Credit DECIMAL(18,2) NOT NULL CHECK (Credit >= 0),
    AccountID INT NOT NULL,
    ReferenceType VARCHAR(50),
    ReferenceID INT,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_journal_account FOREIGN KEY (AccountID) REFERENCES Accounts(AccountID),
    CONSTRAINT chk_debit_credit CHECK ((Debit = 0 AND Credit > 0) OR (Debit > 0 AND Credit = 0))
);

CREATE TABLE IF NOT EXISTS audit_log (
    log_id SERIAL PRIMARY KEY,
    table_name VARCHAR(50),
    operation VARCHAR(10),
    old_data JSONB,
    new_data JSONB,
    changed_by INT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_users_username ON Users(Username);
CREATE INDEX IF NOT EXISTS idx_users_role ON Users(Role);
CREATE INDEX IF NOT EXISTS idx_customers_name ON Customers(Name);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON Customers(Phone);
CREATE INDEX IF NOT EXISTS idx_suppliers_name ON Suppliers(Name);
CREATE INDEX IF NOT EXISTS idx_products_name ON Products(Name);
CREATE INDEX IF NOT EXISTS idx_products_category ON Products(Category);
CREATE INDEX IF NOT EXISTS idx_sales_invoices_date ON SalesInvoices(Date);
CREATE INDEX IF NOT EXISTS idx_sales_invoices_customer ON SalesInvoices(CustomerID);
CREATE INDEX IF NOT EXISTS idx_sales_invoices_user ON SalesInvoices(UserID);
CREATE INDEX IF NOT EXISTS idx_purchase_invoices_date ON PurchaseInvoices(Date);
CREATE INDEX IF NOT EXISTS idx_purchase_invoices_supplier ON PurchaseInvoices(SupplierID);
CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON JournalEntries(Date);
CREATE INDEX IF NOT EXISTS idx_journal_entries_account ON JournalEntries(AccountID);
CREATE INDEX IF NOT EXISTS idx_sales_details_invoice ON SalesInvoiceDetails(InvoiceID);
CREATE INDEX IF NOT EXISTS idx_sales_details_product ON SalesInvoiceDetails(ProductID);
CREATE INDEX IF NOT EXISTS idx_purchase_details_invoice ON PurchaseInvoiceDetails(InvoiceID);
CREATE INDEX IF NOT EXISTS idx_purchase_details_product ON PurchaseInvoiceDetails(ProductID);
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_customer_phone ON Customers(Phone) WHERE IsActive = TRUE;
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_supplier_phone ON Suppliers(Phone) WHERE IsActive = TRUE;

-- Sequences
CREATE SEQUENCE IF NOT EXISTS invoice_number_seq START WITH 1001;
CREATE SEQUENCE IF NOT EXISTS customer_number_seq START WITH 1001;
CREATE SEQUENCE IF NOT EXISTS supplier_number_seq START WITH 1001;

-- Functions (ensure these are created BEFORE triggers)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.UpdatedAt = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION calculate_sales_invoice_total()
RETURNS TRIGGER AS $$
DECLARE
    inv_id INT;
    new_total DECIMAL(18,2);
BEGIN
    IF TG_OP = 'DELETE' THEN
        inv_id := OLD.InvoiceID;
    ELSE
        inv_id := NEW.InvoiceID;
    END IF;

    new_total := COALESCE((SELECT SUM(Total) FROM SalesInvoiceDetails WHERE InvoiceID = inv_id), 0);

    UPDATE SalesInvoices
    SET TotalAmount = new_total,
        RemainingAmount = GREATEST(new_total - COALESCE(PaidAmount,0), 0),
        UpdatedAt = CURRENT_TIMESTAMP
    WHERE InvoiceID = inv_id;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION calculate_purchase_invoice_total()
RETURNS TRIGGER AS $$
DECLARE
    inv_id INT;
    new_total DECIMAL(18,2);
BEGIN
    IF TG_OP = 'DELETE' THEN
        inv_id := OLD.InvoiceID;
    ELSE
        inv_id := NEW.InvoiceID;
    END IF;

    new_total := COALESCE((SELECT SUM(Total) FROM PurchaseInvoiceDetails WHERE InvoiceID = inv_id), 0);

    UPDATE PurchaseInvoices
    SET TotalAmount = new_total,
        RemainingAmount = GREATEST(new_total - COALESCE(PaidAmount,0), 0),
        UpdatedAt = CURRENT_TIMESTAMP
    WHERE InvoiceID = inv_id;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_product_quantity_sales()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE Products 
        SET Quantity = Quantity - NEW.Quantity,
            UpdatedAt = CURRENT_TIMESTAMP
        WHERE ProductID = NEW.ProductID;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE Products 
        SET Quantity = Quantity + OLD.Quantity,
            UpdatedAt = CURRENT_TIMESTAMP
        WHERE ProductID = OLD.ProductID;
    ELSIF TG_OP = 'UPDATE' THEN
        UPDATE Products 
        SET Quantity = Quantity + OLD.Quantity - NEW.Quantity,
            UpdatedAt = CURRENT_TIMESTAMP
        WHERE ProductID = NEW.ProductID;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_product_quantity_purchase()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE Products 
        SET Quantity = Quantity + NEW.Quantity,
            UpdatedAt = CURRENT_TIMESTAMP
        WHERE ProductID = NEW.ProductID;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE Products 
        SET Quantity = Quantity - OLD.Quantity,
            UpdatedAt = CURRENT_TIMESTAMP
        WHERE ProductID = OLD.ProductID;
    ELSIF TG_OP = 'UPDATE' THEN
        UPDATE Products 
        SET Quantity = Quantity - OLD.Quantity + NEW.Quantity,
            UpdatedAt = CURRENT_TIMESTAMP
        WHERE ProductID = NEW.ProductID;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_balances()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_TABLE_NAME = 'salesinvoices' THEN
        IF TG_OP = 'INSERT' THEN
            UPDATE Customers 
            SET Balance = Balance + NEW.RemainingAmount,
                UpdatedAt = CURRENT_TIMESTAMP
            WHERE CustomerID = NEW.CustomerID;
        ELSIF TG_OP = 'UPDATE' THEN
            UPDATE Customers 
            SET Balance = Balance + (NEW.RemainingAmount - OLD.RemainingAmount),
                UpdatedAt = CURRENT_TIMESTAMP
            WHERE CustomerID = NEW.CustomerID;
        ELSIF TG_OP = 'DELETE' THEN
            UPDATE Customers 
            SET Balance = Balance - OLD.RemainingAmount,
                UpdatedAt = CURRENT_TIMESTAMP
            WHERE CustomerID = OLD.CustomerID;
        END IF;
    ELSIF TG_TABLE_NAME = 'purchaseinvoices' THEN
        IF TG_OP = 'INSERT' THEN
            UPDATE Suppliers 
            SET Balance = Balance + NEW.RemainingAmount,
                UpdatedAt = CURRENT_TIMESTAMP
            WHERE SupplierID = NEW.SupplierID;
        ELSIF TG_OP = 'UPDATE' THEN
            UPDATE Suppliers 
            SET Balance = Balance + (NEW.RemainingAmount - OLD.RemainingAmount),
                UpdatedAt = CURRENT_TIMESTAMP
            WHERE SupplierID = NEW.SupplierID;
        ELSIF TG_OP = 'DELETE' THEN
            UPDATE Suppliers 
            SET Balance = Balance - OLD.RemainingAmount,
                UpdatedAt = CURRENT_TIMESTAMP
            WHERE SupplierID = OLD.SupplierID;
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
    v_user_id INT;
BEGIN
    v_user_id := NULLIF(current_setting('app.user_id', true), '')::INT;

    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (table_name, operation, new_data, changed_by)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(NEW), v_user_id);
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log (table_name, operation, old_data, new_data, changed_by)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD), row_to_json(NEW), v_user_id);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (table_name, operation, old_data, changed_by)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD), v_user_id);
        RETURN OLD;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Triggers (create AFTER functions exist)
CREATE TRIGGER trigger_users_updated_at BEFORE UPDATE ON Users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_customers_updated_at BEFORE UPDATE ON Customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_suppliers_updated_at BEFORE UPDATE ON Suppliers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_products_updated_at BEFORE UPDATE ON Products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_accounts_updated_at BEFORE UPDATE ON Accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_sales_details_total AFTER INSERT OR UPDATE OR DELETE ON SalesInvoiceDetails FOR EACH ROW EXECUTE FUNCTION calculate_sales_invoice_total();
CREATE TRIGGER trigger_purchase_details_total AFTER INSERT OR UPDATE OR DELETE ON PurchaseInvoiceDetails FOR EACH ROW EXECUTE FUNCTION calculate_purchase_invoice_total();
CREATE TRIGGER trigger_update_product_quantity_sales AFTER INSERT OR UPDATE OR DELETE ON SalesInvoiceDetails FOR EACH ROW EXECUTE FUNCTION update_product_quantity_sales();
CREATE TRIGGER trigger_update_product_quantity_purchase AFTER INSERT OR UPDATE OR DELETE ON PurchaseInvoiceDetails FOR EACH ROW EXECUTE FUNCTION update_product_quantity_purchase();
CREATE TRIGGER trigger_update_customer_balance AFTER INSERT OR UPDATE OR DELETE ON SalesInvoices FOR EACH ROW EXECUTE FUNCTION update_balances();
CREATE TRIGGER trigger_update_supplier_balance AFTER INSERT OR UPDATE OR DELETE ON PurchaseInvoices FOR EACH ROW EXECUTE FUNCTION update_balances();

CREATE TRIGGER audit_users AFTER INSERT OR UPDATE OR DELETE ON Users FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_customers AFTER INSERT OR UPDATE OR DELETE ON Customers FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_suppliers AFTER INSERT OR UPDATE OR DELETE ON Suppliers FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_products AFTER INSERT OR UPDATE OR DELETE ON Products FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_sales_invoices AFTER INSERT OR UPDATE OR DELETE ON SalesInvoices FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_sales_invoice_details AFTER INSERT OR UPDATE OR DELETE ON SalesInvoiceDetails FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_purchase_invoices AFTER INSERT OR UPDATE OR DELETE ON PurchaseInvoices FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_purchase_invoice_details AFTER INSERT OR UPDATE OR DELETE ON PurchaseInvoiceDetails FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_accounts AFTER INSERT OR UPDATE OR DELETE ON Accounts FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_journal_entries AFTER INSERT OR UPDATE OR DELETE ON JournalEntries FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- Initial data
INSERT INTO Users (Username, PasswordHash, Role) VALUES
('admin', crypt('admin123', gen_salt('bf')), 'مدير'),
('accountant', crypt('acc123', gen_salt('bf')), 'محاسب'),
('seller', crypt('seller123', gen_salt('bf')), 'بائع')
ON CONFLICT (Username) DO NOTHING;

INSERT INTO Accounts (Name, Type) VALUES
('النقدية', 'أصول'),
('العملاء', 'أصول'),
('الموردين', 'خصوم'),
('المبيعات', 'إيرادات'),
('المشتريات', 'مصاريف')
ON CONFLICT (Name) DO NOTHING;

-- Grants
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO accounting_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO accounting_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO accounting_user;

-- Final notice
DO $$
BEGIN
    RAISE NOTICE 'Accounting system schema created/verified. Run VACUUM ANALYZE and adjust configuration as needed.';
END;
$$;
