# -*- coding: utf-8 -*-
"""
Suppliers management UI module - Similar to customers but for suppliers
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
                            QHeaderView, QGroupBox, QTextEdit, QCheckBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from db.repositories import SuppliersRepository
from config import UI_CONFIG, MESSAGES
import logging

logger = logging.getLogger(__name__)

class SupplierDialog(QDialog):
    """Dialog for adding/editing suppliers"""
    
    def __init__(self, supplier_data=None, parent=None):
        super().__init__(parent)
        self.supplier_data = supplier_data
        self.is_edit_mode = supplier_data is not None
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_supplier_data()
    
    def setup_ui(self):
        """Setup dialog UI"""
        title = "تعديل مورد" if self.is_edit_mode else "إضافة مورد جديد"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(450, 350)
        
        layout = QVBoxLayout(self)
        
        # Form
        form_group = QGroupBox("بيانات المورد")
        form_layout = QFormLayout(form_group)
        
        # Name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم المورد")
        form_layout.addRow("اسم المورد:", self.name_edit)
        
        # Phone
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف")
        form_layout.addRow("رقم الهاتف:", self.phone_edit)
        
        # Address
        self.address_edit = QTextEdit()
        self.address_edit.setPlaceholderText("أدخل العنوان")
        self.address_edit.setMaximumHeight(80)
        form_layout.addRow("العنوان:", self.address_edit)
        
        # Active status (for edit mode)
        if self.is_edit_mode:
            self.active_checkbox = QCheckBox("المورد نشط")
            form_layout.addRow("الحالة:", self.active_checkbox)
            
            # Show current balance (read-only)
            self.balance_label = QLabel("0.00")
            self.balance_label.setStyleSheet("font-weight: bold; color: #007bff;")
            form_layout.addRow("الرصيد الحالي:", self.balance_label)
        
        layout.addWidget(form_group)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.button(QDialogButtonBox.Ok).setText("حفظ")
        button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
        self.button_box = button_box
    
    def setup_connections(self):
        """Setup signal connections"""
        self.button_box.accepted.connect(self.validate_and_accept)
        self.button_box.rejected.connect(self.reject)
    
    def load_supplier_data(self):
        """Load supplier data for editing"""
        if self.supplier_data:
            self.name_edit.setText(self.supplier_data.get('name', ''))
            self.phone_edit.setText(self.supplier_data.get('phone', ''))
            self.address_edit.setPlainText(self.supplier_data.get('address', ''))
            
            if hasattr(self, 'active_checkbox'):
                self.active_checkbox.setChecked(self.supplier_data.get('isactive', True))
            
            if hasattr(self, 'balance_label'):
                balance = self.supplier_data.get('balance', 0)
                self.balance_label.setText(f"{balance:.2f}")
    
    def validate_and_accept(self):
        """Validate form data and accept dialog"""
        name = self.name_edit.text().strip()
        phone = self.phone_edit.text().strip()
        
        if not name:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المورد")
            return
        
        if not phone:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الهاتف")
            return
        
        self.accept()
    
    def get_form_data(self):
        """Get form data"""
        data = {
            'Name': self.name_edit.text().strip(),
            'Phone': self.phone_edit.text().strip(),
            'Address': self.address_edit.toPlainText().strip()
        }
        
        if self.is_edit_mode and hasattr(self, 'active_checkbox'):
            data['IsActive'] = self.active_checkbox.isChecked()
        
        return data

class SuppliersWidget(QWidget):
    """Main suppliers management widget"""
    
    def __init__(self, current_user_data, parent=None):
        super().__init__(parent)
        self.current_user_data = current_user_data
        self.suppliers_repo = SuppliersRepository()
        self.setup_ui()
        self.setup_connections()
        self.load_suppliers()
    
    def setup_ui(self):
        """Setup widget UI"""
        layout = QVBoxLayout(self)
        
        # Toolbar
        toolbar_layout = QHBoxLayout()
        
        self.add_button = QPushButton("إضافة مورد")
        self.edit_button = QPushButton("تعديل")
        self.delete_button = QPushButton("حذف")
        self.refresh_button = QPushButton("تحديث")
        self.balance_button = QPushButton("عرض الأرصدة")
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في الموردين...")
        self.search_button = QPushButton("بحث")
        
        toolbar_layout.addWidget(self.add_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addWidget(self.delete_button)
        toolbar_layout.addWidget(self.balance_button)
        toolbar_layout.addWidget(self.refresh_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(QLabel("البحث:"))
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(self.search_button)
        
        # Table
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "رقم المورد", "اسم المورد", "رقم الهاتف", "العنوان", "الرصيد", "الحالة"
        ])
        
        # Configure table
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        
        layout.addLayout(toolbar_layout)
        layout.addWidget(self.table)
        
        # Apply styles (similar to customers)
        self.apply_styles()
    
    def setup_connections(self):
        """Setup signal connections"""
        self.add_button.clicked.connect(self.add_supplier)
        self.edit_button.clicked.connect(self.edit_supplier)
        self.delete_button.clicked.connect(self.delete_supplier)
        self.refresh_button.clicked.connect(self.load_suppliers)
        self.balance_button.clicked.connect(self.show_balances)
        self.search_button.clicked.connect(self.search_suppliers)
        self.search_edit.returnPressed.connect(self.search_suppliers)
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        self.table.itemDoubleClicked.connect(self.edit_supplier)
    
    def apply_styles(self):
        """Apply styles to the widget"""
        self.setStyleSheet(f"""
            QPushButton {{
                padding: 8px 16px;
                margin: 2px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{ opacity: 0.8; }}
            QPushButton#add {{ background-color: {UI_CONFIG['success_color']}; color: white; }}
            QPushButton#edit {{ background-color: {UI_CONFIG['primary_color']}; color: white; }}
            QPushButton#delete {{ background-color: {UI_CONFIG['danger_color']}; color: white; }}
            QPushButton#balance {{ background-color: {UI_CONFIG['secondary_color']}; color: white; }}
            QPushButton#refresh {{ background-color: #6c757d; color: white; }}
            QLineEdit {{ padding: 6px; border: 1px solid #ddd; border-radius: 4px; }}
            QTableWidget {{ gridline-color: #ddd; background-color: white; alternate-background-color: #f8f9fa; }}
            QTableWidget::item {{ padding: 8px; }}
            QHeaderView::section {{ background-color: {UI_CONFIG['primary_color']}; color: white; padding: 8px; border: none; font-weight: bold; }}
        """)
        
        self.add_button.setObjectName("add")
        self.edit_button.setObjectName("edit")
        self.delete_button.setObjectName("delete")
        self.balance_button.setObjectName("balance")
        self.refresh_button.setObjectName("refresh")
    
    def load_suppliers(self):
        """Load suppliers from database"""
        try:
            suppliers = self.suppliers_repo.get_suppliers_with_balances()
            self.populate_table(suppliers)
        except Exception as e:
            logger.error(f"Error loading suppliers: {e}")
            QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def populate_table(self, suppliers):
        """Populate table with supplier data"""
        self.table.setRowCount(len(suppliers))
        
        for row, supplier in enumerate(suppliers):
            self.table.setItem(row, 0, QTableWidgetItem(str(supplier['supplierid'])))
            self.table.setItem(row, 1, QTableWidgetItem(supplier['name']))
            self.table.setItem(row, 2, QTableWidgetItem(supplier['phone']))
            self.table.setItem(row, 3, QTableWidgetItem(supplier.get('address', '')))
            
            # Balance with formatting
            balance = float(supplier.get('balance', 0))
            balance_item = QTableWidgetItem(f"{balance:.2f}")
            if balance > 0:
                balance_item.setBackground(Qt.lightGreen)
            elif balance < 0:
                balance_item.setBackground(Qt.lightCoral)
            self.table.setItem(row, 4, balance_item)
            
            # Status
            status = "نشط" if supplier.get('isactive', True) else "غير نشط"
            status_item = QTableWidgetItem(status)
            if not supplier.get('isactive', True):
                status_item.setBackground(Qt.lightGray)
            self.table.setItem(row, 5, status_item)
            
            # Store supplier data in first column
            self.table.item(row, 0).setData(Qt.UserRole, supplier)
    
    def on_selection_changed(self):
        """Handle table selection change"""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
    
    def get_selected_supplier(self):
        """Get selected supplier data"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            return self.table.item(current_row, 0).data(Qt.UserRole)
        return None
    
    def add_supplier(self):
        """Add new supplier"""
        dialog = SupplierDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                form_data = dialog.get_form_data()
                self.suppliers_repo.insert(form_data)
                QMessageBox.information(self, "نجح", MESSAGES['success']['save'])
                self.load_suppliers()
            except Exception as e:
                logger.error(f"Error adding supplier: {e}")
                if "duplicate" in str(e).lower() or "unique" in str(e).lower():
                    QMessageBox.warning(self, "خطأ", "رقم الهاتف مستخدم مسبقاً")
                else:
                    QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def edit_supplier(self):
        """Edit selected supplier"""
        supplier_data = self.get_selected_supplier()
        if not supplier_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للتعديل")
            return
        
        dialog = SupplierDialog(supplier_data, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                form_data = dialog.get_form_data()
                self.suppliers_repo.update('SupplierID', supplier_data['supplierid'], form_data)
                QMessageBox.information(self, "نجح", MESSAGES['success']['update'])
                self.load_suppliers()
            except Exception as e:
                logger.error(f"Error updating supplier: {e}")
                QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def delete_supplier(self):
        """Delete selected supplier"""
        supplier_data = self.get_selected_supplier()
        if not supplier_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد", 
                                   f"هل تريد حذف المورد '{supplier_data['name']}'؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                self.suppliers_repo.soft_delete('SupplierID', supplier_data['supplierid'])
                QMessageBox.information(self, "نجح", "تم حذف المورد")
                self.load_suppliers()
            except Exception as e:
                logger.error(f"Error deleting supplier: {e}")
                QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def show_balances(self):
        """Show suppliers with balances only"""
        try:
            suppliers = self.suppliers_repo.get_suppliers_with_balances()
            suppliers_with_balance = [s for s in suppliers if float(s.get('balance', 0)) != 0]
            self.populate_table(suppliers_with_balance)
        except Exception as e:
            logger.error(f"Error loading supplier balances: {e}")
            QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def search_suppliers(self):
        """Search suppliers"""
        search_term = self.search_edit.text().strip()
        if not search_term:
            self.load_suppliers()
            return
        
        try:
            suppliers = self.suppliers_repo.search(['Name', 'Phone'], search_term)
            self.populate_table(suppliers)
        except Exception as e:
            logger.error(f"Error searching suppliers: {e}")
            QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
