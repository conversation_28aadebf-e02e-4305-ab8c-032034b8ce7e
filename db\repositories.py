# -*- coding: utf-8 -*-
"""
Repository classes for all database entities
Contains specific business logic for each table
"""

from typing import List, Dict, Any, Optional
from decimal import Decimal
from datetime import datetime, date
from .connection import BaseRepository, db_manager
import logging

logger = logging.getLogger(__name__)

class UsersRepository(BaseRepository):
    """Repository for Users table"""
    
    def __init__(self):
        super().__init__('Users')
    
    def create_user(self, username: str, password: str, role: str) -> Optional[Dict]:
        """Create new user with encrypted password"""
        query = """
        INSERT INTO Users (Username, PasswordHash, Role) 
        VALUES (%s, crypt(%s, gen_salt('bf')), %s) 
        RETURNING UserID, Username, Role, CreatedAt, IsActive
        """
        return self.execute_query(query, (username, password, role))[0]
    
    def update_password(self, user_id: int, new_password: str) -> int:
        """Update user password"""
        query = "UPDATE Users SET PasswordHash = crypt(%s, gen_salt('bf')) WHERE UserID = %s"
        return self.execute_query(query, (new_password, user_id), fetch=False)
    
    def get_active_users(self) -> List[Dict]:
        """Get all active users"""
        return self.get_all("IsActive = TRUE", order_by="Username")
    
    def deactivate_user(self, user_id: int) -> int:
        """Deactivate user"""
        return self.soft_delete('UserID', user_id)

class CustomersRepository(BaseRepository):
    """Repository for Customers table"""
    
    def __init__(self):
        super().__init__('Customers')
    
    def get_active_customers(self) -> List[Dict]:
        """Get all active customers"""
        return self.get_all("IsActive = TRUE", order_by="Name")
    
    def get_customer_with_balance(self, customer_id: int) -> Optional[Dict]:
        """Get customer with current balance"""
        query = """
        SELECT c.*, 
               COALESCE(SUM(si.RemainingAmount), 0) as CurrentBalance
        FROM Customers c
        LEFT JOIN SalesInvoices si ON c.CustomerID = si.CustomerID AND si.Status != 'ملغاة'
        WHERE c.CustomerID = %s AND c.IsActive = TRUE
        GROUP BY c.CustomerID
        """
        result = self.execute_query(query, (customer_id,))
        return result[0] if result else None
    
    def get_customers_with_balances(self) -> List[Dict]:
        """Get all customers with their current balances"""
        query = """
        SELECT c.CustomerID, c.Name, c.Phone, c.Address,
               COALESCE(SUM(si.RemainingAmount), 0) as Balance
        FROM Customers c
        LEFT JOIN SalesInvoices si ON c.CustomerID = si.CustomerID AND si.Status != 'ملغاة'
        WHERE c.IsActive = TRUE
        GROUP BY c.CustomerID, c.Name, c.Phone, c.Address
        ORDER BY c.Name
        """
        return self.execute_query(query)
    
    def search_customers(self, search_term: str) -> List[Dict]:
        """Search customers by name or phone"""
        return self.search(['Name', 'Phone'], search_term)

class SuppliersRepository(BaseRepository):
    """Repository for Suppliers table"""
    
    def __init__(self):
        super().__init__('Suppliers')
    
    def get_active_suppliers(self) -> List[Dict]:
        """Get all active suppliers"""
        return self.get_all("IsActive = TRUE", order_by="Name")
    
    def get_suppliers_with_balances(self) -> List[Dict]:
        """Get all suppliers with their current balances"""
        query = """
        SELECT s.SupplierID, s.Name, s.Phone, s.Address,
               COALESCE(SUM(pi.RemainingAmount), 0) as Balance
        FROM Suppliers s
        LEFT JOIN PurchaseInvoices pi ON s.SupplierID = pi.SupplierID AND pi.Status != 'ملغاة'
        WHERE s.IsActive = TRUE
        GROUP BY s.SupplierID, s.Name, s.Phone, s.Address
        ORDER BY s.Name
        """
        return self.execute_query(query)

class ProductsRepository(BaseRepository):
    """Repository for Products table"""
    
    def __init__(self):
        super().__init__('Products')
    
    def get_active_products(self) -> List[Dict]:
        """Get all active products"""
        return self.get_all("IsActive = TRUE", order_by="Name")
    
    def get_low_stock_products(self) -> List[Dict]:
        """Get products below reorder level"""
        query = """
        SELECT ProductID, Name, Category, Unit, Quantity, ReorderLevel,
               (ReorderLevel - Quantity) as ShortageAmount
        FROM Products 
        WHERE IsActive = TRUE AND Quantity <= ReorderLevel AND ReorderLevel > 0
        ORDER BY (ReorderLevel - Quantity) DESC
        """
        return self.execute_query(query)
    
    def update_quantity(self, product_id: int, quantity_change: Decimal, operation: str = 'add') -> int:
        """Update product quantity"""
        if operation == 'add':
            query = "UPDATE Products SET Quantity = Quantity + %s WHERE ProductID = %s"
        else:
            query = "UPDATE Products SET Quantity = Quantity - %s WHERE ProductID = %s"
        
        return self.execute_query(query, (quantity_change, product_id), fetch=False)
    
    def get_products_by_category(self, category: str) -> List[Dict]:
        """Get products by category"""
        return self.get_all("Category = %s AND IsActive = TRUE", (category,), "Name")

class SalesInvoicesRepository(BaseRepository):
    """Repository for Sales Invoices"""
    
    def __init__(self):
        super().__init__('SalesInvoices')
    
    def create_invoice_with_details(self, invoice_data: Dict, details: List[Dict]) -> Optional[Dict]:
        """Create sales invoice with details in a transaction"""
        try:
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Insert invoice
                    invoice_query = """
                    INSERT INTO SalesInvoices (CustomerID, UserID, Status) 
                    VALUES (%(CustomerID)s, %(UserID)s, %(Status)s) 
                    RETURNING InvoiceID, Date
                    """
                    cursor.execute(invoice_query, invoice_data)
                    invoice_result = cursor.fetchone()
                    invoice_id = invoice_result['invoiceid']
                    
                    # Insert details
                    for detail in details:
                        detail['InvoiceID'] = invoice_id
                        detail_query = """
                        INSERT INTO SalesInvoiceDetails (InvoiceID, ProductID, Quantity, Price, Total)
                        VALUES (%(InvoiceID)s, %(ProductID)s, %(Quantity)s, %(Price)s, %(Total)s)
                        """
                        cursor.execute(detail_query, detail)
                    
                    conn.commit()
                    return dict(invoice_result)
                    
        except Exception as e:
            logger.error(f"Error creating sales invoice: {e}")
            raise
    
    def get_invoice_with_details(self, invoice_id: int) -> Optional[Dict]:
        """Get invoice with its details"""
        query = """
        SELECT si.*, c.Name as CustomerName, u.Username
        FROM SalesInvoices si
        JOIN Customers c ON si.CustomerID = c.CustomerID
        JOIN Users u ON si.UserID = u.UserID
        WHERE si.InvoiceID = %s
        """
        invoice = self.execute_query(query, (invoice_id,))
        
        if invoice:
            details_query = """
            SELECT sid.*, p.Name as ProductName, p.Unit
            FROM SalesInvoiceDetails sid
            JOIN Products p ON sid.ProductID = p.ProductID
            WHERE sid.InvoiceID = %s
            ORDER BY sid.DetailID
            """
            details = self.execute_query(details_query, (invoice_id,))
            
            result = dict(invoice[0])
            result['details'] = details
            return result
        
        return None
    
    def get_daily_sales(self, target_date: date) -> List[Dict]:
        """Get daily sales report"""
        query = """
        SELECT si.InvoiceID, c.Name as CustomerName, si.Date, 
               si.TotalAmount, si.PaidAmount, si.RemainingAmount, si.Status
        FROM SalesInvoices si
        JOIN Customers c ON si.CustomerID = c.CustomerID
        WHERE DATE(si.Date) = %s
        ORDER BY si.Date DESC
        """
        return self.execute_query(query, (target_date,))

class PurchaseInvoicesRepository(BaseRepository):
    """Repository for Purchase Invoices"""
    
    def __init__(self):
        super().__init__('PurchaseInvoices')
    
    def create_invoice_with_details(self, invoice_data: Dict, details: List[Dict]) -> Optional[Dict]:
        """Create purchase invoice with details in a transaction"""
        try:
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Insert invoice
                    invoice_query = """
                    INSERT INTO PurchaseInvoices (SupplierID, UserID, Status) 
                    VALUES (%(SupplierID)s, %(UserID)s, %(Status)s) 
                    RETURNING InvoiceID, Date
                    """
                    cursor.execute(invoice_query, invoice_data)
                    invoice_result = cursor.fetchone()
                    invoice_id = invoice_result['invoiceid']
                    
                    # Insert details
                    for detail in details:
                        detail['InvoiceID'] = invoice_id
                        detail_query = """
                        INSERT INTO PurchaseInvoiceDetails (InvoiceID, ProductID, Quantity, Price, Total)
                        VALUES (%(InvoiceID)s, %(ProductID)s, %(Quantity)s, %(Price)s, %(Total)s)
                        """
                        cursor.execute(detail_query, detail)
                    
                    conn.commit()
                    return dict(invoice_result)
                    
        except Exception as e:
            logger.error(f"Error creating purchase invoice: {e}")
            raise
    
    def get_purchases_by_supplier(self) -> List[Dict]:
        """Get purchases grouped by supplier"""
        query = """
        SELECT s.Name as SupplierName,
               COUNT(pi.InvoiceID) as InvoiceCount,
               SUM(pi.TotalAmount) as TotalPurchases,
               SUM(pi.PaidAmount) as TotalPaid,
               SUM(pi.RemainingAmount) as TotalRemaining
        FROM Suppliers s
        LEFT JOIN PurchaseInvoices pi ON s.SupplierID = pi.SupplierID AND pi.Status != 'ملغاة'
        WHERE s.IsActive = TRUE
        GROUP BY s.SupplierID, s.Name
        HAVING COUNT(pi.InvoiceID) > 0
        ORDER BY TotalPurchases DESC
        """
        return self.execute_query(query)

class AccountsRepository(BaseRepository):
    """Repository for Accounts"""
    
    def __init__(self):
        super().__init__('Accounts')
    
    def get_accounts_by_type(self, account_type: str) -> List[Dict]:
        """Get accounts by type"""
        return self.get_all("Type = %s AND IsActive = TRUE", (account_type,), "Name")

class JournalEntriesRepository(BaseRepository):
    """Repository for Journal Entries"""
    
    def __init__(self):
        super().__init__('JournalEntries')
    
    def create_journal_entry(self, entries: List[Dict]) -> bool:
        """Create journal entries (must be balanced)"""
        try:
            # Validate that debits equal credits
            total_debits = sum(Decimal(str(entry.get('Debit', 0))) for entry in entries)
            total_credits = sum(Decimal(str(entry.get('Credit', 0))) for entry in entries)
            
            if total_debits != total_credits:
                raise ValueError("Journal entry is not balanced: debits must equal credits")
            
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    for entry in entries:
                        query = """
                        INSERT INTO JournalEntries (Date, Description, Debit, Credit, AccountID, ReferenceType, ReferenceID)
                        VALUES (%(Date)s, %(Description)s, %(Debit)s, %(Credit)s, %(AccountID)s, %(ReferenceType)s, %(ReferenceID)s)
                        """
                        cursor.execute(query, entry)
                    
                    conn.commit()
                    return True
                    
        except Exception as e:
            logger.error(f"Error creating journal entry: {e}")
            raise

class AuditLogRepository(BaseRepository):
    """Repository for Audit Log"""
    
    def __init__(self):
        super().__init__('audit_log')
    
    def get_audit_logs(self, limit: int = 1000, table_name: str = None, 
                      start_date: date = None, end_date: date = None) -> List[Dict]:
        """Get audit logs with filters"""
        where_conditions = []
        params = []
        
        if table_name:
            where_conditions.append("table_name = %s")
            params.append(table_name)
        
        if start_date:
            where_conditions.append("DATE(changed_at) >= %s")
            params.append(start_date)
        
        if end_date:
            where_conditions.append("DATE(changed_at) <= %s")
            params.append(end_date)
        
        where_clause = " AND ".join(where_conditions) if where_conditions else ""
        
        query = f"""
        SELECT al.*, u.Username as ChangedByUsername
        FROM audit_log al
        LEFT JOIN Users u ON al.changed_by = u.UserID
        """
        
        if where_clause:
            query += f" WHERE {where_clause}"
        
        query += f" ORDER BY changed_at DESC LIMIT {limit}"
        
        return self.execute_query(query, tuple(params) if params else None)
