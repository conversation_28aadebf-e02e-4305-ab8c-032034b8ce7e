# -*- coding: utf-8 -*-
"""
Users management UI module
Handles CRUD operations for users with role-based access control
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QComboBox, QMessageBox, QDialog, QFormLayout,
                            QDialogButtonBox, QHeaderView, QFrame, QGroupBox,
                            QCheckBox, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from db.repositories import UsersRepository
from config import UI_CONFIG, MESSAGES, VALIDATION_RULES
import logging

logger = logging.getLogger(__name__)

class UserDialog(QDialog):
    """Dialog for adding/editing users"""
    
    def __init__(self, user_data=None, parent=None):
        super().__init__(parent)
        self.user_data = user_data
        self.is_edit_mode = user_data is not None
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_user_data()
    
    def setup_ui(self):
        """Setup dialog UI"""
        title = "تعديل مستخدم" if self.is_edit_mode else "إضافة مستخدم جديد"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout(self)
        
        # Form
        form_group = QGroupBox("بيانات المستخدم")
        form_layout = QFormLayout(form_group)
        
        # Username
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        if self.is_edit_mode:
            self.username_edit.setEnabled(False)  # Can't change username
        form_layout.addRow("اسم المستخدم:", self.username_edit)
        
        # Password
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        password_label = "كلمة المرور الجديدة:" if self.is_edit_mode else "كلمة المرور:"
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        form_layout.addRow(password_label, self.password_edit)
        
        # Confirm Password
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.confirm_password_edit.setPlaceholderText("أعد إدخال كلمة المرور")
        form_layout.addRow("تأكيد كلمة المرور:", self.confirm_password_edit)
        
        # Role
        self.role_combo = QComboBox()
        self.role_combo.addItems(["مدير", "محاسب", "بائع"])
        form_layout.addRow("الدور:", self.role_combo)
        
        # Active status (for edit mode)
        if self.is_edit_mode:
            self.active_checkbox = QCheckBox("المستخدم نشط")
            form_layout.addRow("الحالة:", self.active_checkbox)
        
        layout.addWidget(form_group)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.button(QDialogButtonBox.Ok).setText("حفظ")
        button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
        
        self.button_box = button_box
    
    def setup_connections(self):
        """Setup signal connections"""
        self.button_box.accepted.connect(self.validate_and_accept)
        self.button_box.rejected.connect(self.reject)
    
    def load_user_data(self):
        """Load user data for editing"""
        if self.user_data:
            self.username_edit.setText(self.user_data.get('username', ''))
            self.role_combo.setCurrentText(self.user_data.get('role', 'بائع'))
            if hasattr(self, 'active_checkbox'):
                self.active_checkbox.setChecked(self.user_data.get('isactive', True))
    
    def validate_and_accept(self):
        """Validate form data and accept dialog"""
        # Get form data
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        confirm_password = self.confirm_password_edit.text()
        role = self.role_combo.currentText()
        
        # Validate username
        if not self.is_edit_mode:  # Only validate username for new users
            if not username:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم")
                return
            
            # Validate username format
            from config import validate_field
            is_valid, error_msg = validate_field('username', username)
            if not is_valid:
                QMessageBox.warning(self, "خطأ", f"اسم المستخدم غير صحيح: {error_msg}")
                return
        
        # Validate password (only if provided)
        if password or not self.is_edit_mode:
            if not password:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور")
                return
            
            if password != confirm_password:
                QMessageBox.warning(self, "خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                return
            
            # Validate password strength
            from config import validate_field
            is_valid, error_msg = validate_field('password', password)
            if not is_valid:
                QMessageBox.warning(self, "خطأ", f"كلمة المرور غير صحيحة: {error_msg}")
                return
        
        self.accept()
    
    def get_form_data(self):
        """Get form data"""
        data = {
            'username': self.username_edit.text().strip(),
            'role': self.role_combo.currentText()
        }
        
        # Add password if provided
        password = self.password_edit.text()
        if password:
            data['password'] = password
        
        # Add active status for edit mode
        if self.is_edit_mode and hasattr(self, 'active_checkbox'):
            data['isactive'] = self.active_checkbox.isChecked()
        
        return data

class UsersWidget(QWidget):
    """Main users management widget"""
    
    def __init__(self, current_user_data, parent=None):
        super().__init__(parent)
        self.current_user_data = current_user_data
        self.users_repo = UsersRepository()
        self.setup_ui()
        self.setup_connections()
        self.load_users()
    
    def setup_ui(self):
        """Setup widget UI"""
        layout = QVBoxLayout(self)
        
        # Toolbar
        toolbar_layout = QHBoxLayout()
        
        self.add_button = QPushButton("إضافة مستخدم")
        self.edit_button = QPushButton("تعديل")
        self.deactivate_button = QPushButton("إلغاء تفعيل")
        self.refresh_button = QPushButton("تحديث")
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في المستخدمين...")
        self.search_button = QPushButton("بحث")
        
        toolbar_layout.addWidget(self.add_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addWidget(self.deactivate_button)
        toolbar_layout.addWidget(self.refresh_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(QLabel("البحث:"))
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(self.search_button)
        
        # Table
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "رقم المستخدم", "اسم المستخدم", "الدور", "تاريخ الإنشاء", 
            "آخر تحديث", "الحالة"
        ])
        
        # Configure table
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        
        layout.addLayout(toolbar_layout)
        layout.addWidget(self.table)
        
        # Apply styles
        self.apply_styles()
    
    def setup_connections(self):
        """Setup signal connections"""
        self.add_button.clicked.connect(self.add_user)
        self.edit_button.clicked.connect(self.edit_user)
        self.deactivate_button.clicked.connect(self.deactivate_user)
        self.refresh_button.clicked.connect(self.load_users)
        self.search_button.clicked.connect(self.search_users)
        self.search_edit.returnPressed.connect(self.search_users)
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        self.table.itemDoubleClicked.connect(self.edit_user)
    
    def apply_styles(self):
        """Apply styles to the widget"""
        self.setStyleSheet(f"""
            QPushButton {{
                padding: 8px 16px;
                margin: 2px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }}
            
            QPushButton:hover {{
                opacity: 0.8;
            }}
            
            QPushButton#add {{
                background-color: {UI_CONFIG['success_color']};
                color: white;
            }}
            
            QPushButton#edit {{
                background-color: {UI_CONFIG['primary_color']};
                color: white;
            }}
            
            QPushButton#deactivate {{
                background-color: {UI_CONFIG['warning_color']};
                color: white;
            }}
            
            QPushButton#refresh {{
                background-color: #6c757d;
                color: white;
            }}
            
            QLineEdit {{
                padding: 6px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }}
            
            QTableWidget {{
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }}
            
            QTableWidget::item {{
                padding: 8px;
            }}
            
            QHeaderView::section {{
                background-color: {UI_CONFIG['primary_color']};
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }}
        """)
        
        # Set object names for styling
        self.add_button.setObjectName("add")
        self.edit_button.setObjectName("edit")
        self.deactivate_button.setObjectName("deactivate")
        self.refresh_button.setObjectName("refresh")
    
    def load_users(self):
        """Load users from database"""
        try:
            users = self.users_repo.get_all(order_by="Username")
            self.populate_table(users)
        except Exception as e:
            logger.error(f"Error loading users: {e}")
            QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def populate_table(self, users):
        """Populate table with user data"""
        self.table.setRowCount(len(users))
        
        for row, user in enumerate(users):
            self.table.setItem(row, 0, QTableWidgetItem(str(user['userid'])))
            self.table.setItem(row, 1, QTableWidgetItem(user['username']))
            self.table.setItem(row, 2, QTableWidgetItem(user['role']))
            self.table.setItem(row, 3, QTableWidgetItem(str(user['createdat'])[:19]))
            self.table.setItem(row, 4, QTableWidgetItem(str(user['updatedat'])[:19]))
            
            status = "نشط" if user['isactive'] else "غير نشط"
            status_item = QTableWidgetItem(status)
            if not user['isactive']:
                status_item.setBackground(Qt.lightGray)
            self.table.setItem(row, 5, status_item)
            
            # Store user data in first column
            self.table.item(row, 0).setData(Qt.UserRole, user)
    
    def on_selection_changed(self):
        """Handle table selection change"""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.deactivate_button.setEnabled(has_selection)
    
    def get_selected_user(self):
        """Get selected user data"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            return self.table.item(current_row, 0).data(Qt.UserRole)
        return None
    
    def add_user(self):
        """Add new user"""
        dialog = UserDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                form_data = dialog.get_form_data()
                self.users_repo.create_user(
                    form_data['username'],
                    form_data['password'],
                    form_data['role']
                )
                QMessageBox.information(self, "نجح", MESSAGES['success']['save'])
                self.load_users()
            except Exception as e:
                logger.error(f"Error adding user: {e}")
                if "duplicate" in str(e).lower():
                    QMessageBox.warning(self, "خطأ", MESSAGES['error']['duplicate'])
                else:
                    QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def edit_user(self):
        """Edit selected user"""
        user_data = self.get_selected_user()
        if not user_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم للتعديل")
            return
        
        dialog = UserDialog(user_data, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                form_data = dialog.get_form_data()
                
                # Update user data
                update_data = {'Role': form_data['role']}
                if 'isactive' in form_data:
                    update_data['IsActive'] = form_data['isactive']
                
                self.users_repo.update('UserID', user_data['userid'], update_data)
                
                # Update password if provided
                if 'password' in form_data:
                    self.users_repo.update_password(user_data['userid'], form_data['password'])
                
                QMessageBox.information(self, "نجح", MESSAGES['success']['update'])
                self.load_users()
            except Exception as e:
                logger.error(f"Error updating user: {e}")
                QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def deactivate_user(self):
        """Deactivate selected user"""
        user_data = self.get_selected_user()
        if not user_data:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم لإلغاء تفعيله")
            return
        
        if user_data['userid'] == self.current_user_data['userid']:
            QMessageBox.warning(self, "تحذير", "لا يمكنك إلغاء تفعيل حسابك الخاص")
            return
        
        reply = QMessageBox.question(self, "تأكيد", 
                                   f"هل تريد إلغاء تفعيل المستخدم '{user_data['username']}'؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                self.users_repo.deactivate_user(user_data['userid'])
                QMessageBox.information(self, "نجح", "تم إلغاء تفعيل المستخدم")
                self.load_users()
            except Exception as e:
                logger.error(f"Error deactivating user: {e}")
                QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
    
    def search_users(self):
        """Search users"""
        search_term = self.search_edit.text().strip()
        if not search_term:
            self.load_users()
            return
        
        try:
            users = self.users_repo.search(['Username', 'Role'], search_term)
            self.populate_table(users)
        except Exception as e:
            logger.error(f"Error searching users: {e}")
            QMessageBox.critical(self, "خطأ", MESSAGES['error']['connection'])
