# نظام المحاسبة - Accounting System

نظام محاسبة شامل مطور بـ Python و PyQt5 لإدارة العمليات المالية والمحاسبية.

## المميزات الرئيسية

### 🔐 نظام المصادقة
- تسجيل دخول آمن بكلمة مرور مشفرة
- إدارة الأدوار (مدير، محاسب، بائع)
- صلاحيات مختلفة لكل دور

### 👥 إدارة المستخدمين
- إضافة وتعديل وإلغاء تفعيل المستخدمين
- تعيين الأدوار والصلاحيات
- تغيير كلمات المرور

### 🏢 إدارة العملاء والموردين
- إضافة وتعديل وحذف العملاء والموردين
- تتبع الأرصدة والمديونيات
- البحث والتصفية المتقدمة

### 📦 إدارة المنتجات
- إدارة شاملة للمنتجات والمخزون
- تتبع الكميات والحد الأدنى للطلب
- تنبيهات نقص المخزون
- إدارة الأسعار (تكلفة وبيع)

### 💰 فواتير المبيعات والمشتريات
- إنشاء وتعديل الفواتير
- حساب تلقائي للإجماليات
- تتبع المدفوعات والمتبقي
- ربط تلقائي بالمخزون

### 📊 النظام المحاسبي
- إدارة دليل الحسابات
- القيود المحاسبية
- الميزان التجريبي

### 📈 التقارير
- تقارير المبيعات اليومية والشهرية
- تقارير أرصدة العملاء
- تقارير المنتجات تحت الحد الأدنى
- تقارير المشتريات حسب المورد
- تصدير للـ Excel و PDF

### 🔍 سجل المراجعة
- تتبع جميع العمليات والتغييرات
- سجل شامل للمراجعة والتدقيق

## متطلبات النظام

### قاعدة البيانات
- PostgreSQL 12 أو أحدث
- قاعدة بيانات: `accounting_system`
- المستخدم: `postgres`
- كلمة المرور: `0981135anas`

### Python
- Python 3.8 أو أحدث
- PyQt5
- psycopg2-binary
- openpyxl (للتصدير إلى Excel)
- reportlab (للتصدير إلى PDF)

## التثبيت والتشغيل

### 1. تثبيت قاعدة البيانات

```bash
# تثبيت PostgreSQL (على Ubuntu/Debian)
sudo apt update
sudo apt install postgresql postgresql-contrib

# تشغيل PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# إنشاء قاعدة البيانات
sudo -u postgres psql
CREATE DATABASE accounting_system;
\q
```

### 2. إعداد قاعدة البيانات

```bash
# تشغيل سكريبت إنشاء قاعدة البيانات
sudo -u postgres psql -d accounting_system -f accounting_system_full_install.txt
```

### 3. تثبيت Python والمكتبات

```bash
# إنشاء بيئة افتراضية (اختياري ولكن مُوصى به)
python -m venv accounting_env
source accounting_env/bin/activate  # على Linux/Mac
# أو
accounting_env\Scripts\activate  # على Windows

# تثبيت المكتبات المطلوبة
pip install -r requirements.txt
```

### 4. تشغيل التطبيق

```bash
python main.py
```

## بيانات تسجيل الدخول الافتراضية

| المستخدم | كلمة المرور | الدور |
|----------|-------------|-------|
| admin | admin123 | مدير |
| accountant | acc123 | محاسب |
| seller | seller123 | بائع |

## هيكل المشروع

```
accounting_system2/
├── main.py                 # نقطة دخول التطبيق
├── config.py              # إعدادات التطبيق
├── requirements.txt       # المكتبات المطلوبة
├── README.md             # هذا الملف
├── db/                   # وحدة قاعدة البيانات
│   ├── __init__.py
│   ├── connection.py     # إدارة الاتصال بقاعدة البيانات
│   └── repositories.py  # طبقة الوصول للبيانات
├── ui/                   # واجهات المستخدم
│   ├── __init__.py
│   ├── login_dialog.py   # نافذة تسجيل الدخول
│   ├── main_window.py    # النافذة الرئيسية
│   ├── dashboard_ui.py   # لوحة التحكم
│   ├── users_ui.py       # إدارة المستخدمين
│   ├── customers_ui.py   # إدارة العملاء
│   ├── suppliers_ui.py   # إدارة الموردين
│   ├── products_ui.py    # إدارة المنتجات
│   ├── sales_ui.py       # فواتير المبيعات
│   ├── purchases_ui.py   # فواتير المشتريات
│   ├── accounts_ui.py    # الحسابات والقيود
│   ├── reports_ui.py     # التقارير
│   └── audit_ui.py       # سجل المراجعة
├── reports/              # وحدة التقارير
└── assets/               # الملفات المساعدة (أيقونات، صور)
```

## الاستخدام

### تسجيل الدخول
1. شغل التطبيق باستخدام `python main.py`
2. أدخل اسم المستخدم وكلمة المرور
3. اختر الوحدة المطلوبة من الشريط الجانبي

### إدارة البيانات
- **العملاء**: إضافة وتعديل بيانات العملاء ومتابعة أرصدتهم
- **الموردين**: إدارة بيانات الموردين ومتابعة المستحقات
- **المنتجات**: إدارة المخزون والأسعار والحد الأدنى للطلب
- **المستخدمين**: إدارة حسابات المستخدمين والصلاحيات (للمدير فقط)

### الفواتير
- إنشاء فواتير مبيعات ومشتريات جديدة
- تعديل الفواتير الموجودة
- طباعة وتصدير الفواتير

### التقارير
- عرض التقارير المختلفة
- تصدير التقارير إلى Excel أو PDF
- طباعة التقارير

## الصلاحيات حسب الدور

### مدير
- جميع الصلاحيات
- إدارة المستخدمين
- عرض سجل المراجعة

### محاسب
- إدارة العملاء والموردين
- إدارة المنتجات
- فواتير المبيعات والمشتريات
- الحسابات والقيود
- التقارير

### بائع
- عرض العملاء
- عرض المنتجات
- فواتير المبيعات فقط
- تقارير المبيعات

## استكشاف الأخطاء

### مشاكل قاعدة البيانات
- تأكد من تشغيل PostgreSQL
- تحقق من صحة بيانات الاتصال في `config.py`
- تأكد من إنشاء قاعدة البيانات والجداول

### مشاكل واجهة المستخدم
- تأكد من تثبيت PyQt5 بشكل صحيح
- تحقق من ملف السجل `accounting_system.log`

### مشاكل الأذونات
- تأكد من صلاحيات المستخدم في قاعدة البيانات
- تحقق من الدور المُعين للمستخدم

## التطوير والتوسيع

### إضافة وحدات جديدة
1. أنشئ ملف UI جديد في مجلد `ui/`
2. أضف Repository جديد في `db/repositories.py`
3. أضف الوحدة إلى `main_window.py`

### تخصيص التصميم
- عدّل الألوان والخطوط في `config.py`
- أضف ملفات CSS مخصصة
- استخدم أيقونات مخصصة في مجلد `assets/`

## الدعم والمساهمة

لأي استفسارات أو مشاكل، يرجى:
1. مراجعة ملف السجل `accounting_system.log`
2. التأكد من متطلبات النظام
3. التحقق من إعدادات قاعدة البيانات

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتجارية. يمكن استخدامه وتعديله حسب الحاجة.

---

**ملاحظة**: هذا الإصدار الأول من النظام. بعض الوحدات قيد التطوير وسيتم إكمالها في الإصدارات القادمة.
