# -*- coding: utf-8 -*-
"""
Purchase Invoices UI module - Placeholder for now
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from config import UI_CONFIG

class PurchasesWidget(QWidget):
    """Purchase invoices management widget - Placeholder"""
    
    def __init__(self, current_user_data, parent=None):
        super().__init__(parent)
        self.current_user_data = current_user_data
        self.setup_ui()
    
    def setup_ui(self):
        """Setup placeholder UI"""
        layout = QVBoxLayout(self)
        
        label = QLabel("وحدة فواتير المشتريات\nقيد التطوير")
        label.setAlignment(Qt.AlignCenter)
        label.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['header_font_size']))
        label.setStyleSheet(f"color: {UI_CONFIG['text_color']};")
        
        layout.addWidget(label)
