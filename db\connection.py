# -*- coding: utf-8 -*-
"""
Database connection module for the Accounting System
Handles PostgreSQL connections with connection pooling and error handling
"""

import psycopg2
import psycopg2.pool
from psycopg2.extras import RealDictCursor
from contextlib import contextmanager
import logging
from typing import Optional, Dict, Any, List, Tuple
import threading
from config import DATABASE_CONFIG, MESSAGES

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database connection manager with connection pooling"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(DatabaseManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.connection_pool = None
            self.current_user_id = None
            self.initialized = True
            self._initialize_pool()
    
    def _initialize_pool(self):
        """Initialize connection pool"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                self.connection_pool = psycopg2.pool.ThreadedConnectionPool(
                    minconn=1,
                    maxconn=20,
                    host=DATABASE_CONFIG['host'],
                    port=DATABASE_CONFIG['port'],
                    database=DATABASE_CONFIG['database'],
                    user=DATABASE_CONFIG['user'],
                    password=DATABASE_CONFIG['password'],
                    cursor_factory=RealDictCursor,
                    # Add connection timeout and keepalive settings
                    connect_timeout=10,
                    options="-c statement_timeout=30000"  # 30 second timeout
                )
                logger.info("Database connection pool initialized successfully")
                return
            except psycopg2.OperationalError as e:
                retry_count += 1
                logger.warning(f"Database connection attempt {retry_count} failed: {e}")
                if retry_count >= max_retries:
                    logger.error(f"Failed to initialize database connection pool after {max_retries} attempts")
                    raise ConnectionError(f"Cannot connect to database: {e}")
                import time
                time.sleep(2)  # Wait 2 seconds before retry
            except Exception as e:
                logger.error(f"Failed to initialize database connection pool: {e}")
                raise
    
    @contextmanager
    def get_connection(self):
        """Get database connection from pool"""
        connection = None
        try:
            if not self.connection_pool:
                raise ConnectionError("Database connection pool not initialized")

            connection = self.connection_pool.getconn()
            if connection.closed:
                # Connection is closed, try to get a new one
                self.connection_pool.putconn(connection, close=True)
                connection = self.connection_pool.getconn()

            if self.current_user_id:
                # Set user context for audit logging
                try:
                    with connection.cursor() as cursor:
                        cursor.execute("SET app.user_id = %s", (self.current_user_id,))
                except Exception as e:
                    logger.warning(f"Failed to set user context: {e}")

            yield connection

        except psycopg2.OperationalError as e:
            if connection:
                connection.rollback()
            logger.error(f"Database operational error: {e}")
            raise ConnectionError(f"Database connection lost: {e}")
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if connection:
                try:
                    self.connection_pool.putconn(connection)
                except Exception as e:
                    logger.error(f"Error returning connection to pool: {e}")
    
    def set_current_user(self, user_id: int):
        """Set current user for audit logging"""
        self.current_user_id = user_id
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    return True
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False

    def get_connection_info(self) -> Dict[str, Any]:
        """Get database connection information"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT
                            version() as version,
                            current_database() as database,
                            current_user as user,
                            inet_server_addr() as server_addr,
                            inet_server_port() as server_port
                    """)
                    result = cursor.fetchone()
                    return dict(result) if result else {}
        except Exception as e:
            logger.error(f"Failed to get connection info: {e}")
            return {}

    def check_database_health(self) -> Dict[str, Any]:
        """Comprehensive database health check"""
        health_info = {
            'connection_status': False,
            'pool_status': False,
            'tables_exist': False,
            'users_exist': False,
            'error_messages': []
        }

        try:
            # Test basic connection
            health_info['connection_status'] = self.test_connection()

            # Check connection pool
            if self.connection_pool:
                health_info['pool_status'] = True

            # Check if required tables exist
            required_tables = ['users', 'customers', 'suppliers', 'products']
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT table_name
                        FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = ANY(%s)
                    """, (required_tables,))

                    existing_tables = [row['table_name'] for row in cursor.fetchall()]
                    health_info['tables_exist'] = len(existing_tables) >= len(required_tables)
                    health_info['existing_tables'] = existing_tables

                    # Check if users exist
                    cursor.execute("SELECT COUNT(*) as count FROM users")
                    user_count = cursor.fetchone()['count']
                    health_info['users_exist'] = user_count > 0
                    health_info['user_count'] = user_count

        except Exception as e:
            health_info['error_messages'].append(str(e))
            logger.error(f"Database health check failed: {e}")

        return health_info
    
    def close_all_connections(self):
        """Close all connections in the pool"""
        if self.connection_pool:
            self.connection_pool.closeall()
            logger.info("All database connections closed")

# Global database manager instance
db_manager = DatabaseManager()

class BaseRepository:
    """Base repository class with common CRUD operations"""
    
    def __init__(self, table_name: str):
        self.table_name = table_name
        self.db_manager = db_manager
    
    def execute_query(self, query: str, params: tuple = None, fetch: bool = True) -> Optional[List[Dict]]:
        """Execute a database query with improved error handling"""
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, params)
                    if fetch:
                        result = cursor.fetchall()
                        return result if result else []
                    else:
                        conn.commit()
                        return cursor.rowcount
        except psycopg2.IntegrityError as e:
            logger.error(f"Database integrity error: {e}")
            raise ValueError(f"Data integrity violation: {e}")
        except psycopg2.DataError as e:
            logger.error(f"Database data error: {e}")
            raise ValueError(f"Invalid data format: {e}")
        except psycopg2.OperationalError as e:
            logger.error(f"Database operational error: {e}")
            raise ConnectionError(f"Database connection issue: {e}")
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise
    
    def get_all(self, where_clause: str = "", params: tuple = None, order_by: str = "") -> List[Dict]:
        """Get all records from table"""
        query = f"SELECT * FROM {self.table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        if order_by:
            query += f" ORDER BY {order_by}"
        
        return self.execute_query(query, params)
    
    def get_by_id(self, id_column: str, id_value: Any) -> Optional[Dict]:
        """Get record by ID"""
        query = f"SELECT * FROM {self.table_name} WHERE {id_column} = %s"
        result = self.execute_query(query, (id_value,))
        return result[0] if result else None
    
    def insert(self, data: Dict[str, Any]) -> int:
        """Insert new record"""
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['%s'] * len(data))
        query = f"INSERT INTO {self.table_name} ({columns}) VALUES ({placeholders}) RETURNING *"
        
        result = self.execute_query(query, tuple(data.values()))
        return result[0] if result else None
    
    def update(self, id_column: str, id_value: Any, data: Dict[str, Any]) -> int:
        """Update existing record"""
        set_clause = ', '.join([f"{key} = %s" for key in data.keys()])
        query = f"UPDATE {self.table_name} SET {set_clause} WHERE {id_column} = %s"
        
        params = tuple(data.values()) + (id_value,)
        return self.execute_query(query, params, fetch=False)
    
    def delete(self, id_column: str, id_value: Any) -> int:
        """Delete record"""
        query = f"DELETE FROM {self.table_name} WHERE {id_column} = %s"
        return self.execute_query(query, (id_value,), fetch=False)
    
    def soft_delete(self, id_column: str, id_value: Any) -> int:
        """Soft delete record (set IsActive = FALSE)"""
        query = f"UPDATE {self.table_name} SET IsActive = FALSE WHERE {id_column} = %s"
        return self.execute_query(query, (id_value,), fetch=False)
    
    def search(self, search_columns: List[str], search_term: str, limit: int = 100) -> List[Dict]:
        """Search records in specified columns"""
        search_conditions = []
        params = []
        
        for column in search_columns:
            search_conditions.append(f"{column}::text ILIKE %s")
            params.append(f"%{search_term}%")
        
        where_clause = " OR ".join(search_conditions)
        query = f"SELECT * FROM {self.table_name} WHERE {where_clause} LIMIT %s"
        params.append(limit)
        
        return self.execute_query(query, tuple(params))
    
    def count(self, where_clause: str = "", params: tuple = None) -> int:
        """Count records"""
        query = f"SELECT COUNT(*) as count FROM {self.table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        
        result = self.execute_query(query, params)
        return result[0]['count'] if result else 0

def authenticate_user(username: str, password: str) -> Optional[Dict]:
    """Authenticate user login with improved error handling"""
    try:
        query = """
        SELECT UserID, Username, Role, IsActive
        FROM Users
        WHERE Username = %s AND PasswordHash = crypt(%s, PasswordHash) AND IsActive = TRUE
        """

        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, (username, password))
                result = cursor.fetchone()

                if result:
                    # Handle case sensitivity in column names
                    user_data = {
                        'userid': result.get('userid') or result.get('UserID'),
                        'username': result.get('username') or result.get('Username'),
                        'role': result.get('role') or result.get('Role'),
                        'isactive': result.get('isactive') or result.get('IsActive')
                    }

                    db_manager.set_current_user(user_data['userid'])
                    logger.info(f"User {username} authenticated successfully")
                    return user_data
                else:
                    logger.warning(f"Authentication failed for user {username}")
                    return None

    except psycopg2.OperationalError as e:
        logger.error(f"Database connection error during authentication: {e}")
        return None
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        return None

def get_user_permissions(role: str) -> List[str]:
    """Get user permissions based on role"""
    from config import USER_ROLES
    return USER_ROLES.get(role, [])

def check_permission(user_role: str, required_permission: str) -> bool:
    """Check if user has required permission"""
    permissions = get_user_permissions(user_role)
    return required_permission in permissions

# Initialize database connection on module import
try:
    if not db_manager.test_connection():
        logger.error("Failed to establish database connection")
except Exception as e:
    logger.error(f"Database initialization error: {e}")
