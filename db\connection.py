# -*- coding: utf-8 -*-
"""
Database connection module for the Accounting System
Handles PostgreSQL connections with connection pooling and error handling
"""

import psycopg2
import psycopg2.pool
from psycopg2.extras import RealDictCursor
from contextlib import contextmanager
import logging
from typing import Optional, Dict, Any, List, Tuple
import threading
from config import DATABASE_CONFIG, MESSAGES

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database connection manager with connection pooling"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(DatabaseManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.connection_pool = None
            self.current_user_id = None
            self.initialized = True
            self._initialize_pool()
    
    def _initialize_pool(self):
        """Initialize connection pool"""
        try:
            self.connection_pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=1,
                maxconn=20,
                host=DATABASE_CONFIG['host'],
                port=DATABASE_CONFIG['port'],
                database=DATABASE_CONFIG['database'],
                user=DATABASE_CONFIG['user'],
                password=DATABASE_CONFIG['password'],
                cursor_factory=RealDictCursor
            )
            logger.info("Database connection pool initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize database connection pool: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get database connection from pool"""
        connection = None
        try:
            connection = self.connection_pool.getconn()
            if self.current_user_id:
                # Set user context for audit logging
                with connection.cursor() as cursor:
                    cursor.execute("SET app.user_id = %s", (self.current_user_id,))
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if connection:
                self.connection_pool.putconn(connection)
    
    def set_current_user(self, user_id: int):
        """Set current user for audit logging"""
        self.current_user_id = user_id
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    return True
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
    
    def close_all_connections(self):
        """Close all connections in the pool"""
        if self.connection_pool:
            self.connection_pool.closeall()
            logger.info("All database connections closed")

# Global database manager instance
db_manager = DatabaseManager()

class BaseRepository:
    """Base repository class with common CRUD operations"""
    
    def __init__(self, table_name: str):
        self.table_name = table_name
        self.db_manager = db_manager
    
    def execute_query(self, query: str, params: tuple = None, fetch: bool = True) -> Optional[List[Dict]]:
        """Execute a database query"""
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, params)
                    if fetch:
                        return cursor.fetchall()
                    else:
                        conn.commit()
                        return cursor.rowcount
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise
    
    def get_all(self, where_clause: str = "", params: tuple = None, order_by: str = "") -> List[Dict]:
        """Get all records from table"""
        query = f"SELECT * FROM {self.table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        if order_by:
            query += f" ORDER BY {order_by}"
        
        return self.execute_query(query, params)
    
    def get_by_id(self, id_column: str, id_value: Any) -> Optional[Dict]:
        """Get record by ID"""
        query = f"SELECT * FROM {self.table_name} WHERE {id_column} = %s"
        result = self.execute_query(query, (id_value,))
        return result[0] if result else None
    
    def insert(self, data: Dict[str, Any]) -> int:
        """Insert new record"""
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['%s'] * len(data))
        query = f"INSERT INTO {self.table_name} ({columns}) VALUES ({placeholders}) RETURNING *"
        
        result = self.execute_query(query, tuple(data.values()))
        return result[0] if result else None
    
    def update(self, id_column: str, id_value: Any, data: Dict[str, Any]) -> int:
        """Update existing record"""
        set_clause = ', '.join([f"{key} = %s" for key in data.keys()])
        query = f"UPDATE {self.table_name} SET {set_clause} WHERE {id_column} = %s"
        
        params = tuple(data.values()) + (id_value,)
        return self.execute_query(query, params, fetch=False)
    
    def delete(self, id_column: str, id_value: Any) -> int:
        """Delete record"""
        query = f"DELETE FROM {self.table_name} WHERE {id_column} = %s"
        return self.execute_query(query, (id_value,), fetch=False)
    
    def soft_delete(self, id_column: str, id_value: Any) -> int:
        """Soft delete record (set IsActive = FALSE)"""
        query = f"UPDATE {self.table_name} SET IsActive = FALSE WHERE {id_column} = %s"
        return self.execute_query(query, (id_value,), fetch=False)
    
    def search(self, search_columns: List[str], search_term: str, limit: int = 100) -> List[Dict]:
        """Search records in specified columns"""
        search_conditions = []
        params = []
        
        for column in search_columns:
            search_conditions.append(f"{column}::text ILIKE %s")
            params.append(f"%{search_term}%")
        
        where_clause = " OR ".join(search_conditions)
        query = f"SELECT * FROM {self.table_name} WHERE {where_clause} LIMIT %s"
        params.append(limit)
        
        return self.execute_query(query, tuple(params))
    
    def count(self, where_clause: str = "", params: tuple = None) -> int:
        """Count records"""
        query = f"SELECT COUNT(*) as count FROM {self.table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        
        result = self.execute_query(query, params)
        return result[0]['count'] if result else 0

def authenticate_user(username: str, password: str) -> Optional[Dict]:
    """Authenticate user login"""
    try:
        query = """
        SELECT UserID, Username, Role, IsActive 
        FROM Users 
        WHERE Username = %s AND PasswordHash = crypt(%s, PasswordHash) AND IsActive = TRUE
        """
        
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, (username, password))
                result = cursor.fetchone()
                
                if result:
                    db_manager.set_current_user(result['userid'])
                    logger.info(f"User {username} authenticated successfully")
                    return dict(result)
                else:
                    logger.warning(f"Authentication failed for user {username}")
                    return None
                    
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        return None

def get_user_permissions(role: str) -> List[str]:
    """Get user permissions based on role"""
    from config import USER_ROLES
    return USER_ROLES.get(role, [])

def check_permission(user_role: str, required_permission: str) -> bool:
    """Check if user has required permission"""
    permissions = get_user_permissions(user_role)
    return required_permission in permissions

# Initialize database connection on module import
try:
    if not db_manager.test_connection():
        logger.error("Failed to establish database connection")
except Exception as e:
    logger.error(f"Database initialization error: {e}")
