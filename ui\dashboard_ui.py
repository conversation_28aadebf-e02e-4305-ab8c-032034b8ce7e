# -*- coding: utf-8 -*-
"""
Dashboard UI module - Main overview screen
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QFrame, QGridLayout, QPushButton, QMessageBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from db.repositories import (CustomersRepository, SuppliersRepository, 
                           ProductsRepository, SalesInvoicesRepository)
from config import UI_CONFIG, MESSAGES
import logging

logger = logging.getLogger(__name__)

class DashboardWidget(QWidget):
    """Dashboard widget showing system overview"""
    
    def __init__(self, current_user_data, parent=None):
        super().__init__(parent)
        self.current_user_data = current_user_data
        self.setup_repositories()
        self.setup_ui()
        self.setup_timer()
        self.load_dashboard_data()
    
    def setup_repositories(self):
        """Initialize repository instances"""
        self.customers_repo = CustomersRepository()
        self.suppliers_repo = SuppliersRepository()
        self.products_repo = ProductsRepository()
        self.sales_repo = SalesInvoicesRepository()
    
    def setup_ui(self):
        """Setup dashboard UI"""
        layout = QVBoxLayout(self)
        
        # Welcome message
        welcome_label = QLabel(f"مرحباً {self.current_user_data['username']}")
        welcome_label.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['title_font_size'], QFont.Bold))
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet(f"color: {UI_CONFIG['primary_color']}; margin: 20px;")
        
        # Statistics grid
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.StyledPanel)
        stats_layout = QGridLayout(stats_frame)
        
        # Create stat cards
        self.customers_card = self.create_stat_card("العملاء", "0", UI_CONFIG['primary_color'])
        self.suppliers_card = self.create_stat_card("الموردين", "0", UI_CONFIG['secondary_color'])
        self.products_card = self.create_stat_card("المنتجات", "0", UI_CONFIG['success_color'])
        self.low_stock_card = self.create_stat_card("نواقص المخزون", "0", UI_CONFIG['warning_color'])
        
        stats_layout.addWidget(self.customers_card, 0, 0)
        stats_layout.addWidget(self.suppliers_card, 0, 1)
        stats_layout.addWidget(self.products_card, 1, 0)
        stats_layout.addWidget(self.low_stock_card, 1, 1)
        
        # Quick actions
        actions_frame = QFrame()
        actions_frame.setFrameStyle(QFrame.StyledPanel)
        actions_layout = QVBoxLayout(actions_frame)
        
        actions_title = QLabel("الإجراءات السريعة")
        actions_title.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['header_font_size'], QFont.Bold))
        actions_title.setAlignment(Qt.AlignCenter)
        
        actions_buttons_layout = QHBoxLayout()
        
        self.new_sale_btn = QPushButton("فاتورة مبيعات جديدة")
        self.new_purchase_btn = QPushButton("فاتورة مشتريات جديدة")
        self.view_reports_btn = QPushButton("عرض التقارير")
        self.refresh_btn = QPushButton("تحديث البيانات")
        
        actions_buttons_layout.addWidget(self.new_sale_btn)
        actions_buttons_layout.addWidget(self.new_purchase_btn)
        actions_buttons_layout.addWidget(self.view_reports_btn)
        actions_buttons_layout.addWidget(self.refresh_btn)
        
        actions_layout.addWidget(actions_title)
        actions_layout.addLayout(actions_buttons_layout)
        
        # Add to main layout
        layout.addWidget(welcome_label)
        layout.addWidget(stats_frame)
        layout.addWidget(actions_frame)
        layout.addStretch()
        
        # Apply styles
        self.apply_styles()
        
        # Setup connections
        self.setup_connections()
    
    def create_stat_card(self, title: str, value: str, color: str) -> QFrame:
        """Create a statistics card"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                margin: 5px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        
        # Value label
        value_label = QLabel(value)
        value_label.setFont(QFont(UI_CONFIG['font_family'], 24, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color};")
        
        # Title label
        title_label = QLabel(title)
        title_label.setFont(QFont(UI_CONFIG['font_family'], UI_CONFIG['header_font_size']))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"color: {UI_CONFIG['text_color']};")
        
        layout.addWidget(value_label)
        layout.addWidget(title_label)
        
        # Store labels for updating
        card.value_label = value_label
        card.title_label = title_label
        
        return card
    
    def setup_connections(self):
        """Setup signal connections"""
        self.refresh_btn.clicked.connect(self.load_dashboard_data)
        self.new_sale_btn.clicked.connect(self.open_sales_module)
        self.new_purchase_btn.clicked.connect(self.open_purchases_module)
        self.view_reports_btn.clicked.connect(self.open_reports_module)
    
    def setup_timer(self):
        """Setup auto-refresh timer"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_dashboard_data)
        self.refresh_timer.start(300000)  # Refresh every 5 minutes
    
    def apply_styles(self):
        """Apply styles to dashboard"""
        self.setStyleSheet(f"""
            QPushButton {{
                padding: 10px 20px;
                margin: 5px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                background-color: {UI_CONFIG['primary_color']};
                color: white;
            }}
            
            QPushButton:hover {{
                opacity: 0.8;
            }}
            
            QFrame {{
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                margin: 10px;
                padding: 10px;
            }}
        """)
    
    def load_dashboard_data(self):
        """Load dashboard statistics"""
        try:
            # Load customers count
            customers = self.customers_repo.get_active_customers()
            self.customers_card.value_label.setText(str(len(customers)))
            
            # Load suppliers count
            suppliers = self.suppliers_repo.get_active_suppliers()
            self.suppliers_card.value_label.setText(str(len(suppliers)))
            
            # Load products count
            products = self.products_repo.get_active_products()
            self.products_card.value_label.setText(str(len(products)))
            
            # Load low stock products count
            low_stock = self.products_repo.get_low_stock_products()
            self.low_stock_card.value_label.setText(str(len(low_stock)))
            
            # Update low stock card color based on count
            if len(low_stock) > 0:
                self.low_stock_card.value_label.setStyleSheet(f"color: {UI_CONFIG['danger_color']};")
            else:
                self.low_stock_card.value_label.setStyleSheet(f"color: {UI_CONFIG['success_color']};")
                
        except Exception as e:
            logger.error(f"Error loading dashboard data: {e}")
            QMessageBox.warning(self, "تحذير", "حدث خطأ في تحميل بيانات لوحة التحكم")
    
    def open_sales_module(self):
        """Open sales module"""
        # This would be implemented to switch to sales module
        QMessageBox.information(self, "معلومات", "سيتم فتح وحدة المبيعات")
    
    def open_purchases_module(self):
        """Open purchases module"""
        # This would be implemented to switch to purchases module
        QMessageBox.information(self, "معلومات", "سيتم فتح وحدة المشتريات")
    
    def open_reports_module(self):
        """Open reports module"""
        # This would be implemented to switch to reports module
        QMessageBox.information(self, "معلومات", "سيتم فتح وحدة التقارير")
