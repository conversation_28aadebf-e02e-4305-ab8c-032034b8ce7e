# -*- coding: utf-8 -*-
"""
Main entry point for the Accounting System Desktop Application
Handles application initialization, login, and main window management
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont
from config import APP_CONFIG, UI_CONFIG, MESSAGES
from ui.login_dialog import LoginDialog
from ui.main_window import MainWindow
from db.connection import db_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('accounting_system.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class AccountingApplication:
    """Main application class"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.current_user = None
        self.splash = None
    
    def initialize_application(self):
        """Initialize PyQt5 application"""
        try:
            # Create QApplication
            self.app = QApplication(sys.argv)
            
            # Set application properties
            self.app.setApplicationName(APP_CONFIG['app_name'])
            self.app.setApplicationVersion(APP_CONFIG['version'])
            self.app.setOrganizationName("Accounting System")
            
            # Set application font
            font = QFont(UI_CONFIG['font_family'], UI_CONFIG['font_size'])
            self.app.setFont(font)
            
            # Set RTL layout if needed
            if UI_CONFIG.get('rtl_support', False):
                self.app.setLayoutDirection(Qt.RightToLeft)
            
            # Show splash screen
            self.show_splash_screen()
            
            logger.info("Application initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize application: {e}")
            return False
    
    def show_splash_screen(self):
        """Show splash screen during startup"""
        try:
            # Create splash screen (you can add an image later)
            splash_pixmap = QPixmap(400, 300)
            splash_pixmap.fill(Qt.white)
            
            self.splash = QSplashScreen(splash_pixmap)
            self.splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
            
            # Add text to splash screen
            self.splash.showMessage(
                f"{APP_CONFIG['app_name']}\nالإصدار {APP_CONFIG['version']}\n\nجاري التحميل...",
                Qt.AlignCenter | Qt.AlignBottom,
                Qt.black
            )
            
            self.splash.show()
            self.app.processEvents()
            
            # Simulate loading time
            QTimer.singleShot(2000, self.splash.close)
            
        except Exception as e:
            logger.warning(f"Could not show splash screen: {e}")
    
    def test_database_connection(self):
        """Test database connection"""
        try:
            if db_manager.test_connection():
                logger.info("Database connection successful")
                return True
            else:
                logger.error("Database connection failed")
                QMessageBox.critical(
                    None, 
                    "خطأ في قاعدة البيانات", 
                    MESSAGES['error']['connection'] + "\n\nيرجى التحقق من إعدادات قاعدة البيانات"
                )
                return False
                
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            QMessageBox.critical(
                None, 
                "خطأ في قاعدة البيانات", 
                f"فشل في الاتصال بقاعدة البيانات:\n{str(e)}"
            )
            return False
    
    def show_login_dialog(self):
        """Show login dialog and handle authentication"""
        try:
            login_dialog = LoginDialog()
            
            def on_login_success(user_data):
                self.current_user = user_data
                logger.info(f"User {user_data['username']} logged in successfully")
                self.show_main_window()
            
            login_dialog.login_successful.connect(on_login_success)
            
            # Show login dialog
            if login_dialog.exec_() == LoginDialog.Accepted:
                return True
            else:
                logger.info("Login cancelled by user")
                return False
                
        except Exception as e:
            logger.error(f"Login dialog error: {e}")
            QMessageBox.critical(
                None, 
                "خطأ", 
                f"حدث خطأ في نافذة تسجيل الدخول:\n{str(e)}"
            )
            return False
    
    def show_main_window(self):
        """Show main application window"""
        try:
            self.main_window = MainWindow(self.current_user)
            
            # Connect logout signal
            self.main_window.user_logout.connect(self.handle_logout)
            
            # Show window
            self.main_window.show()
            
            # Close splash screen if still open
            if self.splash:
                self.splash.close()
            
            logger.info("Main window displayed successfully")
            
        except Exception as e:
            logger.error(f"Failed to show main window: {e}")
            QMessageBox.critical(
                None, 
                "خطأ", 
                f"فشل في عرض النافذة الرئيسية:\n{str(e)}"
            )
    
    def handle_logout(self):
        """Handle user logout"""
        try:
            logger.info(f"User {self.current_user['username']} logged out")
            
            # Close main window
            if self.main_window:
                self.main_window.close()
                self.main_window = None
            
            # Reset current user
            self.current_user = None
            
            # Show login dialog again
            if not self.show_login_dialog():
                self.quit_application()
                
        except Exception as e:
            logger.error(f"Logout error: {e}")
            self.quit_application()
    
    def quit_application(self):
        """Quit application gracefully"""
        try:
            logger.info("Application shutting down")
            
            # Close database connections
            db_manager.close_all_connections()
            
            # Close splash screen if open
            if self.splash:
                self.splash.close()
            
            # Close main window if open
            if self.main_window:
                self.main_window.close()
            
            # Quit application
            if self.app:
                self.app.quit()
                
        except Exception as e:
            logger.error(f"Error during application shutdown: {e}")
            sys.exit(1)
    
    def run(self):
        """Run the application"""
        try:
            # Initialize application
            if not self.initialize_application():
                return 1
            
            # Test database connection
            if not self.test_database_connection():
                return 1
            
            # Show login dialog
            if not self.show_login_dialog():
                return 0
            
            # Run application event loop
            return self.app.exec_()
            
        except KeyboardInterrupt:
            logger.info("Application interrupted by user")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            QMessageBox.critical(
                None, 
                "خطأ غير متوقع", 
                f"حدث خطأ غير متوقع:\n{str(e)}"
            )
            return 1
        finally:
            self.quit_application()

def main():
    """Main function"""
    try:
        # Create and run application
        app = AccountingApplication()
        exit_code = app.run()
        
        logger.info(f"Application exited with code: {exit_code}")
        sys.exit(exit_code)
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
