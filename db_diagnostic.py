#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Diagnostic Tool
Comprehensive database health check and troubleshooting utility
"""

import sys
import json
from datetime import datetime
from typing import Dict, Any

def run_comprehensive_diagnostic():
    """Run comprehensive database diagnostic"""
    print("🔍 Database Diagnostic Tool")
    print("=" * 60)
    print(f"Started at: {datetime.now()}")
    print()
    
    diagnostic_results = {
        'timestamp': datetime.now().isoformat(),
        'tests': {},
        'summary': {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'warnings': 0
        },
        'recommendations': []
    }
    
    # Test 1: Configuration Check
    print("1️⃣  Configuration Check")
    print("-" * 30)
    config_result = test_configuration()
    diagnostic_results['tests']['configuration'] = config_result
    print_test_result(config_result)
    
    # Test 2: Database Connection
    print("\n2️⃣  Database Connection Test")
    print("-" * 30)
    connection_result = test_database_connection()
    diagnostic_results['tests']['connection'] = connection_result
    print_test_result(connection_result)
    
    # Test 3: Schema Validation
    print("\n3️⃣  Database Schema Validation")
    print("-" * 30)
    schema_result = test_database_schema()
    diagnostic_results['tests']['schema'] = schema_result
    print_test_result(schema_result)
    
    # Test 4: Data Integrity
    print("\n4️⃣  Data Integrity Check")
    print("-" * 30)
    integrity_result = test_data_integrity()
    diagnostic_results['tests']['integrity'] = integrity_result
    print_test_result(integrity_result)
    
    # Test 5: Performance Check
    print("\n5️⃣  Performance Analysis")
    print("-" * 30)
    performance_result = test_performance()
    diagnostic_results['tests']['performance'] = performance_result
    print_test_result(performance_result)
    
    # Test 6: Security Check
    print("\n6️⃣  Security Validation")
    print("-" * 30)
    security_result = test_security()
    diagnostic_results['tests']['security'] = security_result
    print_test_result(security_result)
    
    # Calculate summary
    for test_name, test_result in diagnostic_results['tests'].items():
        diagnostic_results['summary']['total_tests'] += 1
        if test_result['status'] == 'PASS':
            diagnostic_results['summary']['passed_tests'] += 1
        elif test_result['status'] == 'FAIL':
            diagnostic_results['summary']['failed_tests'] += 1
        elif test_result['status'] == 'WARNING':
            diagnostic_results['summary']['warnings'] += 1
    
    # Generate recommendations
    diagnostic_results['recommendations'] = generate_recommendations(diagnostic_results['tests'])
    
    # Print summary
    print_summary(diagnostic_results)
    
    # Save diagnostic report
    save_diagnostic_report(diagnostic_results)
    
    return diagnostic_results

def test_configuration():
    """Test application configuration"""
    result = {
        'status': 'PASS',
        'message': 'Configuration is valid',
        'details': [],
        'errors': []
    }
    
    try:
        import config
        
        # Check required configurations
        required_configs = [
            'DATABASE_CONFIG', 'APP_CONFIG', 'UI_CONFIG', 
            'USER_ROLES', 'MESSAGES', 'VALIDATION_RULES'
        ]
        
        for config_name in required_configs:
            if hasattr(config, config_name):
                result['details'].append(f"✓ {config_name} found")
            else:
                result['errors'].append(f"✗ {config_name} missing")
                result['status'] = 'FAIL'
        
        # Validate database config
        db_config = config.DATABASE_CONFIG
        required_db_keys = ['host', 'port', 'database', 'user', 'password']
        
        for key in required_db_keys:
            if key in db_config and db_config[key]:
                result['details'].append(f"✓ Database {key} configured")
            else:
                result['errors'].append(f"✗ Database {key} missing or empty")
                result['status'] = 'FAIL'
        
        if result['status'] == 'PASS':
            result['message'] = 'All configuration checks passed'
        else:
            result['message'] = 'Configuration validation failed'
            
    except ImportError as e:
        result['status'] = 'FAIL'
        result['message'] = 'Cannot import configuration'
        result['errors'].append(f"Import error: {e}")
    except Exception as e:
        result['status'] = 'FAIL'
        result['message'] = 'Configuration test failed'
        result['errors'].append(f"Error: {e}")
    
    return result

def test_database_connection():
    """Test database connection and health"""
    result = {
        'status': 'PASS',
        'message': 'Database connection successful',
        'details': [],
        'errors': []
    }
    
    try:
        from db.connection import db_manager
        
        # Test basic connection
        if db_manager.test_connection():
            result['details'].append("✓ Basic connection test passed")
        else:
            result['errors'].append("✗ Basic connection test failed")
            result['status'] = 'FAIL'
        
        # Test health check
        health_info = db_manager.check_database_health()
        
        if health_info['connection_status']:
            result['details'].append("✓ Connection status healthy")
        else:
            result['errors'].append("✗ Connection status unhealthy")
            result['status'] = 'FAIL'
        
        if health_info['pool_status']:
            result['details'].append("✓ Connection pool initialized")
        else:
            result['errors'].append("✗ Connection pool not initialized")
            result['status'] = 'FAIL'
        
        # Get connection info
        conn_info = db_manager.get_connection_info()
        if conn_info:
            result['details'].append(f"✓ Connected to: {conn_info.get('database', 'unknown')}")
            result['details'].append(f"✓ PostgreSQL version: {conn_info.get('version', 'unknown')[:50]}...")
        
        if result['status'] == 'PASS':
            result['message'] = 'Database connection is healthy'
        else:
            result['message'] = 'Database connection issues detected'
            
    except Exception as e:
        result['status'] = 'FAIL'
        result['message'] = 'Database connection test failed'
        result['errors'].append(f"Error: {e}")
    
    return result

def test_database_schema():
    """Test database schema integrity"""
    result = {
        'status': 'PASS',
        'message': 'Database schema is valid',
        'details': [],
        'errors': []
    }
    
    try:
        from db.connection import db_manager
        
        required_tables = [
            'users', 'customers', 'suppliers', 'products',
            'salesinvoices', 'salesinvoicedetails',
            'purchaseinvoices', 'purchaseinvoicedetails',
            'accounts', 'journalentries', 'audit_log'
        ]
        
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                # Check table existence
                cursor.execute("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """)
                
                existing_tables = [row['table_name'].lower() for row in cursor.fetchall()]
                
                for table in required_tables:
                    if table.lower() in existing_tables:
                        result['details'].append(f"✓ Table '{table}' exists")
                    else:
                        result['errors'].append(f"✗ Table '{table}' missing")
                        if result['status'] == 'PASS':
                            result['status'] = 'WARNING'
                
                # Check for required extensions
                cursor.execute("SELECT extname FROM pg_extension WHERE extname = 'pgcrypto'")
                if cursor.fetchone():
                    result['details'].append("✓ pgcrypto extension installed")
                else:
                    result['errors'].append("✗ pgcrypto extension missing")
                    result['status'] = 'FAIL'
        
        if result['status'] == 'PASS':
            result['message'] = 'All required tables and extensions found'
        elif result['status'] == 'WARNING':
            result['message'] = 'Some optional tables missing'
        else:
            result['message'] = 'Critical schema components missing'
            
    except Exception as e:
        result['status'] = 'FAIL'
        result['message'] = 'Schema validation failed'
        result['errors'].append(f"Error: {e}")
    
    return result

def test_data_integrity():
    """Test data integrity and constraints"""
    result = {
        'status': 'PASS',
        'message': 'Data integrity is good',
        'details': [],
        'errors': []
    }
    
    try:
        from db.repositories import UsersRepository
        
        # Test users table
        users_repo = UsersRepository()
        users = users_repo.get_all()
        
        if users:
            result['details'].append(f"✓ Found {len(users)} users in database")
            
            # Check for admin user
            admin_users = [u for u in users if u.get('role') == 'مدير']
            if admin_users:
                result['details'].append("✓ Admin user exists")
            else:
                result['errors'].append("✗ No admin user found")
                result['status'] = 'WARNING'
        else:
            result['errors'].append("✗ No users found in database")
            result['status'] = 'FAIL'
        
        if result['status'] == 'PASS':
            result['message'] = 'Data integrity checks passed'
        elif result['status'] == 'WARNING':
            result['message'] = 'Minor data integrity issues found'
        else:
            result['message'] = 'Critical data integrity issues found'
            
    except Exception as e:
        result['status'] = 'FAIL'
        result['message'] = 'Data integrity test failed'
        result['errors'].append(f"Error: {e}")
    
    return result

def test_performance():
    """Test database performance"""
    result = {
        'status': 'PASS',
        'message': 'Performance is acceptable',
        'details': [],
        'errors': []
    }
    
    try:
        from db.maintenance import DatabaseMaintenance
        
        maintenance = DatabaseMaintenance()
        performance_info = maintenance.check_database_performance()
        
        # Check connection count
        conn_count = performance_info.get('connection_count', 0)
        if conn_count < 10:
            result['details'].append(f"✓ Connection count is healthy: {conn_count}")
        elif conn_count < 20:
            result['details'].append(f"⚠ Connection count is moderate: {conn_count}")
            result['status'] = 'WARNING'
        else:
            result['errors'].append(f"✗ High connection count: {conn_count}")
            result['status'] = 'FAIL'
        
        # Check active queries
        active_queries = performance_info.get('active_queries', 0)
        if active_queries < 5:
            result['details'].append(f"✓ Active queries count is low: {active_queries}")
        else:
            result['details'].append(f"⚠ Active queries count: {active_queries}")
            if result['status'] == 'PASS':
                result['status'] = 'WARNING'
        
        if result['status'] == 'PASS':
            result['message'] = 'Database performance is good'
        elif result['status'] == 'WARNING':
            result['message'] = 'Performance could be improved'
        else:
            result['message'] = 'Performance issues detected'
            
    except Exception as e:
        result['status'] = 'WARNING'
        result['message'] = 'Performance test incomplete'
        result['errors'].append(f"Error: {e}")
    
    return result

def test_security():
    """Test security configurations"""
    result = {
        'status': 'PASS',
        'message': 'Security configuration is adequate',
        'details': [],
        'errors': []
    }
    
    try:
        from db.connection import authenticate_user
        
        # Test authentication system
        # Try with invalid credentials
        auth_result = authenticate_user('invalid_user', 'invalid_password')
        if auth_result is None:
            result['details'].append("✓ Authentication rejects invalid credentials")
        else:
            result['errors'].append("✗ Authentication security issue")
            result['status'] = 'FAIL'
        
        # Test with valid admin credentials
        auth_result = authenticate_user('admin', 'admin123')
        if auth_result:
            result['details'].append("✓ Admin authentication works")
        else:
            result['errors'].append("✗ Admin authentication failed")
            result['status'] = 'FAIL'
        
        if result['status'] == 'PASS':
            result['message'] = 'Security checks passed'
        else:
            result['message'] = 'Security issues detected'
            
    except Exception as e:
        result['status'] = 'WARNING'
        result['message'] = 'Security test incomplete'
        result['errors'].append(f"Error: {e}")
    
    return result

def generate_recommendations(test_results):
    """Generate recommendations based on test results"""
    recommendations = []
    
    for test_name, result in test_results.items():
        if result['status'] == 'FAIL':
            if test_name == 'connection':
                recommendations.append({
                    'priority': 'HIGH',
                    'category': 'Database Connection',
                    'issue': 'Database connection failed',
                    'solution': 'Check PostgreSQL service, verify connection parameters in config.py',
                    'arabic': 'تحقق من خدمة PostgreSQL وإعدادات الاتصال'
                })
            elif test_name == 'schema':
                recommendations.append({
                    'priority': 'HIGH',
                    'category': 'Database Schema',
                    'issue': 'Database schema incomplete',
                    'solution': 'Run the database installation script (accounting_system_full_install.txt)',
                    'arabic': 'قم بتشغيل سكريبت تثبيت قاعدة البيانات'
                })
        elif result['status'] == 'WARNING':
            if test_name == 'performance':
                recommendations.append({
                    'priority': 'MEDIUM',
                    'category': 'Performance',
                    'issue': 'Performance could be improved',
                    'solution': 'Consider running VACUUM ANALYZE, check connection pooling settings',
                    'arabic': 'فكر في تشغيل VACUUM ANALYZE وفحص إعدادات تجميع الاتصالات'
                })
    
    return recommendations

def print_test_result(result):
    """Print formatted test result"""
    status_symbols = {
        'PASS': '✅',
        'FAIL': '❌',
        'WARNING': '⚠️'
    }
    
    print(f"{status_symbols.get(result['status'], '❓')} {result['message']}")
    
    for detail in result['details']:
        print(f"   {detail}")
    
    for error in result['errors']:
        print(f"   {error}")

def print_summary(diagnostic_results):
    """Print diagnostic summary"""
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    summary = diagnostic_results['summary']
    print(f"Total Tests: {summary['total_tests']}")
    print(f"✅ Passed: {summary['passed_tests']}")
    print(f"❌ Failed: {summary['failed_tests']}")
    print(f"⚠️  Warnings: {summary['warnings']}")
    
    if summary['failed_tests'] == 0 and summary['warnings'] == 0:
        print("\n🎉 All tests passed! Your database is healthy.")
    elif summary['failed_tests'] == 0:
        print("\n✅ No critical issues found, but some warnings to address.")
    else:
        print("\n⚠️  Critical issues found that need immediate attention.")
    
    # Print recommendations
    if diagnostic_results['recommendations']:
        print("\n📋 RECOMMENDATIONS:")
        print("-" * 30)
        for i, rec in enumerate(diagnostic_results['recommendations'], 1):
            print(f"{i}. [{rec['priority']}] {rec['category']}")
            print(f"   Issue: {rec['issue']}")
            print(f"   Solution: {rec['solution']}")
            print(f"   الحل: {rec['arabic']}")
            print()

def save_diagnostic_report(diagnostic_results):
    """Save diagnostic report to file"""
    try:
        filename = f"diagnostic_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(diagnostic_results, f, indent=2, ensure_ascii=False)
        print(f"\n💾 Diagnostic report saved to: {filename}")
    except Exception as e:
        print(f"\n⚠️  Could not save diagnostic report: {e}")

if __name__ == "__main__":
    try:
        run_comprehensive_diagnostic()
    except KeyboardInterrupt:
        print("\n\n⏹️  Diagnostic interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Diagnostic failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
