#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database integration test script
Tests all database operations and identifies potential issues
"""

import sys
import traceback
from datetime import datetime

def test_database_connection():
    """Test basic database connection"""
    print("=" * 50)
    print("1. Testing Database Connection")
    print("=" * 50)
    
    try:
        from db.connection import db_manager
        
        if db_manager.test_connection():
            print("✓ Database connection successful")
            return True
        else:
            print("✗ Database connection failed")
            return False
    except Exception as e:
        print(f"✗ Database connection error: {e}")
        traceback.print_exc()
        return False

def test_authentication():
    """Test user authentication"""
    print("\n" + "=" * 50)
    print("2. Testing User Authentication")
    print("=" * 50)
    
    try:
        from db.connection import authenticate_user
        
        # Test with default admin user
        user_data = authenticate_user('admin', 'admin123')
        if user_data:
            print("✓ Authentication successful")
            print(f"  User: {user_data['username']}")
            print(f"  Role: {user_data['role']}")
            print(f"  Active: {user_data['isactive']}")
            return True
        else:
            print("✗ Authentication failed - checking if users exist...")
            
            # Check if users table has data
            from db.repositories import UsersRepository
            users_repo = UsersRepository()
            users = users_repo.get_all()
            
            if not users:
                print("✗ No users found in database")
                print("  Please run the database installation script first")
            else:
                print(f"✓ Found {len(users)} users in database")
                for user in users:
                    print(f"  - {user['username']} ({user['role']})")
                print("✗ Authentication failed - check password")
            
            return False
            
    except Exception as e:
        print(f"✗ Authentication error: {e}")
        traceback.print_exc()
        return False

def test_repositories():
    """Test repository operations"""
    print("\n" + "=" * 50)
    print("3. Testing Repository Operations")
    print("=" * 50)
    
    try:
        from db.repositories import (UsersRepository, CustomersRepository, 
                                   SuppliersRepository, ProductsRepository)
        
        # Test Users Repository
        print("Testing Users Repository...")
        users_repo = UsersRepository()
        users = users_repo.get_active_users()
        print(f"✓ Found {len(users)} active users")
        
        # Test Customers Repository
        print("Testing Customers Repository...")
        customers_repo = CustomersRepository()
        customers = customers_repo.get_active_customers()
        print(f"✓ Found {len(customers)} active customers")
        
        # Test Suppliers Repository
        print("Testing Suppliers Repository...")
        suppliers_repo = SuppliersRepository()
        suppliers = suppliers_repo.get_active_suppliers()
        print(f"✓ Found {len(suppliers)} active suppliers")
        
        # Test Products Repository
        print("Testing Products Repository...")
        products_repo = ProductsRepository()
        products = products_repo.get_active_products()
        print(f"✓ Found {len(products)} active products")
        
        return True
        
    except Exception as e:
        print(f"✗ Repository error: {e}")
        traceback.print_exc()
        return False

def test_database_schema():
    """Test database schema compatibility"""
    print("\n" + "=" * 50)
    print("4. Testing Database Schema")
    print("=" * 50)
    
    try:
        from db.connection import db_manager
        
        # Test table existence
        tables_to_check = [
            'users', 'customers', 'suppliers', 'products',
            'salesinvoices', 'salesinvoicedetails',
            'purchaseinvoices', 'purchaseinvoicedetails',
            'accounts', 'journalentries', 'audit_log'
        ]
        
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                for table in tables_to_check:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = 'public'
                            AND table_name = %s
                        ) as table_exists;
                    """, (table,))

                    result = cursor.fetchone()
                    exists = result['table_exists'] if result else False
                    if exists:
                        print(f"✓ Table '{table}' exists")
                    else:
                        print(f"✗ Table '{table}' missing")
        
        return True
        
    except Exception as e:
        print(f"✗ Schema check error: {e}")
        traceback.print_exc()
        return False

def test_ui_imports():
    """Test UI module imports"""
    print("\n" + "=" * 50)
    print("5. Testing UI Module Imports")
    print("=" * 50)
    
    ui_modules = [
        'ui.login_dialog',
        'ui.main_window',
        'ui.dashboard_ui',
        'ui.users_ui',
        'ui.customers_ui',
        'ui.suppliers_ui',
        'ui.products_ui'
    ]
    
    success_count = 0
    
    for module in ui_modules:
        try:
            __import__(module)
            print(f"✓ {module} imported successfully")
            success_count += 1
        except Exception as e:
            print(f"✗ {module} import failed: {e}")
    
    print(f"\nImport Summary: {success_count}/{len(ui_modules)} modules imported successfully")
    return success_count == len(ui_modules)

def test_configuration():
    """Test configuration settings"""
    print("\n" + "=" * 50)
    print("6. Testing Configuration")
    print("=" * 50)
    
    try:
        import config
        
        # Check database config
        db_config = config.DATABASE_CONFIG
        print("Database Configuration:")
        print(f"  Host: {db_config['host']}")
        print(f"  Port: {db_config['port']}")
        print(f"  Database: {db_config['database']}")
        print(f"  User: {db_config['user']}")
        print(f"  Password: {'*' * len(db_config['password'])}")
        
        # Check if all required config sections exist
        required_configs = [
            'DATABASE_CONFIG', 'APP_CONFIG', 'UI_CONFIG', 
            'USER_ROLES', 'MESSAGES', 'VALIDATION_RULES'
        ]
        
        for config_name in required_configs:
            if hasattr(config, config_name):
                print(f"✓ {config_name} configured")
            else:
                print(f"✗ {config_name} missing")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("Database Integration Test Suite")
    print("=" * 50)
    print(f"Test started at: {datetime.now()}")
    
    tests = [
        test_database_connection,
        test_authentication,
        test_repositories,
        test_database_schema,
        test_ui_imports,
        test_configuration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The application should work correctly.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    print(f"Test completed at: {datetime.now()}")

if __name__ == "__main__":
    main()
